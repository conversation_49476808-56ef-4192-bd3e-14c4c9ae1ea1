{"name": "superlytics", "version": "2.18.1", "description": "A modern, privacy-focused alternative to Google Analytics.", "author": "1804 Audio", "scripts": {"dev": "next dev", "dev-turbo": "next dev -p 3001 --turbopack", "build": "npm-run-all check-env build-db check-db build-tracker build-geo build-app", "start": "next start", "build-docker": "npm-run-all build-db build-tracker build-geo build-app", "start-docker": "npm-run-all check-db update-tracker set-routes-manifest start-server", "start-env": "node scripts/start-env.js", "start-server": "node server.js", "build-app": "next build", "build-components": "rollup -c rollup.components.config.mjs", "build-tracker": "rollup -c rollup.tracker.config.mjs", "build-db": "npm-run-all copy-db-files build-db-client", "build-lang": "npm-run-all format-lang compile-lang download-country-names download-language-names clean-lang", "build-geo": "node scripts/build-geo.js", "build-db-schema": "prisma db pull", "build-db-client": "prisma generate", "set-routes-manifest": "node scripts/set-routes-manifest.js", "update-tracker": "node scripts/update-tracker.js", "update-db": "prisma migrate deploy", "check-db": "node scripts/check-db.js", "check-env": "node scripts/check-env.js", "copy-db-files": "node scripts/copy-db-files.js", "extract-messages": "formatjs extract \"src/components/messages.ts\" --out-file build/extracted-messages.json", "merge-messages": "node scripts/merge-messages.js", "generate-lang": "npm-run-all extract-messages merge-messages", "format-lang": "node scripts/format-lang.js", "compile-lang": "formatjs compile-folder --ast build/messages public/intl/messages", "clean-lang": "prettier --write ./public/intl/**/*.json", "check-lang": "node scripts/check-lang.js", "download-country-names": "node scripts/download-country-names.js", "download-language-names": "node scripts/download-language-names.js", "change-password": "node scripts/change-password.js", "lint": "next lint --quiet", "prepare": "node -e \"if (process.env.NODE_ENV !== 'production'){process.exit(1)} \" || husky install", "postbuild": "node scripts/postbuild.js", "test": "jest", "cypress-open": "cypress open cypress run", "cypress-run": "cypress run cypress run"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": ["prettier --write", "eslint"], "**/*.css": ["stylelint --fix", "prettier --write"], "**/*.json": ["prettier --write"]}, "cacheDirectories": [".next/cache"], "dependencies": {"@clickhouse/client": "^1.11.2", "@date-fns/utc": "^2.1.0", "@dicebear/collection": "^9.2.3", "@dicebear/core": "^9.2.3", "@fontsource/inter": "^5.2.6", "@hello-pangea/dnd": "^18.0.1", "@prisma/client": "6.12.0", "@prisma/extension-read-replicas": "^0.4.1", "@react-spring/web": "^10.0.1", "@tanstack/react-query": "^5.83.0", "@umami/redis-client": "^0.27.0", "bcryptjs": "^3.0.2", "chalk": "^5.4.1", "chart.js": "^4.5.0", "chartjs-adapter-date-fns": "^3.0.0", "classnames": "^2.5.1", "colord": "^2.9.3", "cors": "^2.8.5", "cross-spawn": "^7.0.6", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "debug": "^4.4.1", "del": "^8.0.0", "detect-browser": "^5.3.0", "dotenv": "^17.2.0", "eslint-plugin-promise": "^7.2.1", "fs-extra": "^11.3.0", "immer": "^10.1.1", "ipaddr.js": "^2.2.0", "is-ci": "^4.1.0", "is-docker": "^3.0.0", "is-localhost-ip": "^2.0.0", "isbot": "^5.1.28", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "maxmind": "^4.3.28", "md5": "^2.3.0", "next": "15.4.2", "node-fetch": "^3.3.2", "npm-run-all": "^4.1.5", "prisma": "6.12.0", "pure-rand": "^7.0.1", "react": "^19.1.0", "react-basics": "^0.126.0", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-icons": "^5.5.0", "react-intl": "^7.1.11", "react-simple-maps": "^3.0.0", "react-use-measure": "^2.1.7", "react-window": "^1.8.11", "request-ip": "^3.3.0", "semver": "^7.7.2", "serialize-error": "^12.0.0", "thenby": "^1.3.4", "uuid": "^11.1.0", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@formatjs/cli": "^6.7.2", "@netlify/plugin-nextjs": "^5.11.6", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^28.0.6", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@stylistic/stylelint-plugin": "^3.1.3", "@svgr/rollup": "^8.1.0", "@svgr/webpack": "^8.1.0", "@types/bcryptjs": "^3.0.0", "@types/jest": "^30.0.0", "@types/node": "^24.0.15", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "cross-env": "^7.0.3", "cypress": "^14.5.2", "esbuild": "^0.25.7", "eslint": "^8.57.1", "eslint-config-next": "^15.4.2", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-css-modules": "^2.12.0", "eslint-plugin-cypress": "3.3.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-prettier": "^5.5.1", "extract-react-intl-messages": "^4.1.1", "husky": "^9.1.7", "jest": "^30.0.4", "lint-staged": "^16.1.2", "postcss": "^8.5.6", "postcss-flexbugs-fixes": "^5.0.2", "postcss-import": "^16.1.1", "postcss-preset-env": "10.2.4", "prettier": "^3.6.2", "prompts": "2.4.2", "rollup": "^4.44.2", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-delete": "^3.0.1", "rollup-plugin-dts": "^6.2.1", "rollup-plugin-esbuild": "^6.2.1", "rollup-plugin-node-externals": "^8.0.1", "rollup-plugin-postcss": "^4.0.2", "stylelint": "^16.22.0", "stylelint-config-css-modules": "^4.5.1", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recommended": "^16.0.0", "tar": "^7.4.3", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}