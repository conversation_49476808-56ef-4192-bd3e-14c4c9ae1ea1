version: '3.8'

services:
  superlytics:
    container_name: superlytics
    image: ghcr.io/1804-audio/superlytics:postgresql-latest
    ports:
      - '127.0.0.1:3000:3000'
    environment:
      DATABASE_URL: ${DATABASE_URL}
      DATABASE_TYPE: ${DATABASE_TYPE}
      APP_SECRET: ${APP_SECRET}
    depends_on:
      db:
        condition: service_healthy
    init: true
    restart: always
    healthcheck:
      test: ['CMD-SHELL', 'curl -f http://localhost:3000/api/heartbeat || exit 1']
      interval: 5s
      timeout: 5s
      retries: 5

  db:
    container_name: superlytics-db
    image: docker.io/library/postgres:15-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - superlytics-db-data:/var/lib/postgresql/data:Z
    restart: always
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}']
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  superlytics-db-data:
