{"env": {"browser": true, "es2020": true, "node": true, "jquery": true, "jest": true}, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 11, "sourceType": "module"}, "extends": ["plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "eslint:recommended", "plugin:prettier/recommended", "plugin:import/errors", "plugin:import/typescript", "plugin:css-modules/recommended", "prettier", "next"], "plugins": ["@typescript-eslint", "prettier", "promise", "css-modules", "cypress"], "rules": {"no-console": "error", "react/display-name": "off", "react-hooks/exhaustive-deps": "off", "react/react-in-jsx-scope": "off", "react/prop-types": "off", "import/no-anonymous-default-export": "off", "import/no-named-as-default": "off", "css-modules/no-unused-class": "off", "@next/next/no-img-element": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-unused-vars": ["error", {"ignoreRestSiblings": true}], "@typescript-eslint/no-namespace": ["error", {"allowDeclarations": true}]}, "globals": {"React": "writable"}, "overrides": [{"files": ["cypress/**/*.ts", "cypress/**/*.js"], "extends": ["plugin:cypress/recommended"], "env": {"cypress/globals": true}}, {"files": ["scripts/**/*.js"], "env": {"node": true}, "parser": "espree", "parserOptions": {"ecmaVersion": 2020, "sourceType": "script"}, "rules": {"@typescript-eslint/no-require-imports": "off", "@typescript-eslint/no-unused-vars": "off"}}]}