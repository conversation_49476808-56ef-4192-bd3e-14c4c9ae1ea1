---
services:
  superlytics:
    image: ghcr.io/1804-audio/superlytics:postgresql-latest
    ports:
      - '3000:3000'
    environment:
      DATABASE_URL: ********************************************/superlytics
      DATABASE_TYPE: postgresql
      APP_SECRET: cYC+fxzmKhCAR0SHaQUe2fEW966w5TtUGbNSs6V3/TA=
    depends_on:
      db:
        condition: service_healthy
    init: true
    restart: always
    healthcheck:
      test: ['CMD-SHELL', 'curl http://localhost:3000/api/heartbeat']
      interval: 5s
      timeout: 5s
      retries: 5
  db:
    image: postgres:15-alpine
    ports:
      - '5432:5432'
    environment:
      POSTGRES_DB: superlytics
      POSTGRES_USER: superlytics
      POSTGRES_PASSWORD: superlytics
    volumes:
      - superlytics-db-data:/var/lib/postgresql/data
    restart: always
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U $${POSTGRES_USER} -d $${POSTGRES_DB}']
      interval: 5s
      timeout: 5s
      retries: 5
volumes:
  superlytics-db-data:
