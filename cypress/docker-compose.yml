---
version: '3'
services:
  superlytics:
    build: ../
    #image: ghcr.io/1804-audio/superlytics:postgresql-latest
    ports:
      - '3000:3000'
    environment:
      DATABASE_URL: ********************************************/superlytics
      DATABASE_TYPE: postgresql
      APP_SECRET: replace-me-with-a-random-string
    depends_on:
      db:
        condition: service_healthy
    restart: always
    healthcheck:
      test: ['CMD-SHELL', 'curl http://localhost:3000/api/heartbeat']
      interval: 5s
      timeout: 5s
      retries: 5
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: superlytics
      POSTGRES_USER: superlytics
      POSTGRES_PASSWORD: superlytics
    volumes:
      - superlytics-db-data:/var/lib/postgresql/data
    restart: always
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U $${POSTGRES_USER} -d $${POSTGRES_DB}']
      interval: 5s
      timeout: 5s
      retries: 5
  cypress:
    image: 'cypress/included:13.6.0'
    depends_on:
      - superlytics
      - db
    environment:
      - CYPRESS_baseUrl=http://superlytics:3000
      - CYPRESS_superlytics_user=admin
      - CYPRESS_superlytics_password=superlytics
    volumes:
      - ./tsconfig.json:/tsconfig.json
      - ../cypress.config.ts:/cypress.config.ts
      - ./:/cypress
      - ../node_modules/:/node_modules
      - ../src/lib/crypto.ts:/src/lib/crypto.ts
volumes:
  superlytics-db-data:
