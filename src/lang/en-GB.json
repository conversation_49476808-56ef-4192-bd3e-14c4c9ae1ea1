{"label.access-code": "Access code", "label.actions": "Actions", "label.activity": "Activity log", "label.add": "Add", "label.add-description": "Add description", "label.add-member": "Add member", "label.add-step": "Add step", "label.add-website": "Add website", "label.admin": "Administrator", "label.after": "After", "label.all": "All", "label.all-time": "All time", "label.analytics": "Analytics", "label.average": "Average", "label.back": "Back", "label.before": "Before", "label.bounce-rate": "Bounce rate", "label.breakdown": "Breakdown", "label.browser": "Browser", "label.browsers": "Browsers", "label.cancel": "Cancel", "label.change-password": "Change password", "label.cities": "Cities", "label.city": "City", "label.clear-all": "Clear all", "label.compare": "Compare", "label.confirm": "Confirm", "label.confirm-password": "Confirm password", "label.contains": "Contains", "label.continue": "Continue", "label.count": "Count", "label.countries": "Countries", "label.country": "Country", "label.create": "Create", "label.create-report": "Create report", "label.create-team": "Create team", "label.create-user": "Create user", "label.created": "Created", "label.created-by": "Created By", "label.current": "Current", "label.current-password": "Current password", "label.custom-range": "Custom range", "label.dashboard": "Dashboard", "label.data": "Data", "label.date": "Date", "label.date-range": "Date range", "label.day": "Day", "label.default-date-range": "Default date range", "label.delete": "Delete", "label.delete-report": "Delete report", "label.delete-team": "Delete team", "label.delete-user": "Delete user", "label.delete-website": "Delete website", "label.description": "Description", "label.desktop": "Desktop", "label.details": "Details", "label.device": "<PERSON><PERSON>", "label.devices": "Devices", "label.dismiss": "<PERSON><PERSON><PERSON>", "label.does-not-contain": "Does not contain", "label.domain": "Domain", "label.dropoff": "Dropoff", "label.edit": "Edit", "label.edit-dashboard": "Edit dashboard", "label.edit-member": "Edit member", "label.enable-share-url": "Enable share URL", "label.end-step": "End Step", "label.entry": "Entry URL", "label.event": "Event", "label.event-data": "Event data", "label.events": "Events", "label.exit": "Exit URL", "label.false": "False", "label.field": "Field", "label.fields": "Fields", "label.filter": "Filter", "label.filter-combined": "Combined", "label.filter-raw": "Raw", "label.filters": "Filters", "label.first-seen": "First seen", "label.funnel": "Funnel", "label.funnel-description": "Understand the conversion and drop-off rate of users.", "label.goal": "Goal", "label.goals": "Goals", "label.goals-description": "Track your goals for pageviews and events.", "label.greater-than": "Greater than", "label.greater-than-equals": "Greater than or equals", "label.host": "Host", "label.hosts": "Hosts", "label.insights": "Insights", "label.insights-description": "Dive deeper into your data by using segments and filters.", "label.is": "Is", "label.is-not": "Is not", "label.is-not-set": "Is not set", "label.is-set": "Is set", "label.join": "Join", "label.join-team": "Join team", "label.journey": "Journey", "label.journey-description": "Understand how users navigate through your website.", "label.language": "Language", "label.languages": "Languages", "label.laptop": "Laptop", "label.last-days": "Last {x} days", "label.last-hours": "Last {x} hours", "label.last-months": "Last {x} months", "label.last-seen": "Last seen", "label.leave": "Leave", "label.leave-team": "Leave team", "label.less-than": "Less than", "label.less-than-equals": "Less than or equals", "label.login": "<PERSON><PERSON>", "label.logout": "Logout", "label.manage": "Manage", "label.manager": "Manager", "label.max": "Max", "label.member": "Member", "label.members": "Members", "label.min": "Min", "label.mobile": "Mobile", "label.more": "More", "label.my-account": "My account", "label.my-websites": "My websites", "label.name": "Name", "label.new-password": "New password", "label.none": "None", "label.number-of-records": "{x} {x, plural, one {record} other {records}}", "label.ok": "OK", "label.os": "OS", "label.overview": "Overview", "label.owner": "Owner", "label.page-of": "Page {current} of {total}", "label.page-views": "Page views", "label.pageTitle": "Page title", "label.pages": "Pages", "label.password": "Password", "label.path": "Path", "label.paths": "Paths", "label.powered-by": "Powered by {name}", "label.previous": "Previous", "label.previous-period": "Previous period", "label.previous-year": "Previous year", "label.profile": "Profile", "label.properties": "Properties", "label.property": "Property", "label.queries": "Queries", "label.query": "Query", "label.query-parameters": "Query parameters", "label.realtime": "Realtime", "label.referrer": "<PERSON><PERSON><PERSON>", "label.referrers": "Referrers", "label.refresh": "Refresh", "label.regenerate": "Regenerate", "label.region": "Region", "label.regions": "Regions", "label.remove": "Remove", "label.remove-member": "Remove member", "label.reports": "Reports", "label.required": "Required", "label.reset": "Reset", "label.reset-website": "Reset statistics", "label.retention": "Retention", "label.retention-description": "Measure your website stickiness by tracking how often users return.", "label.revenue": "Revenue", "label.revenue-description": "Look into your revenue across time.", "label.revenue-property": "Revenue Property", "label.role": "Role", "label.run-query": "Run query", "label.save": "Save", "label.screens": "Screens", "label.search": "Search", "label.select": "Select", "label.select-date": "Select date", "label.select-role": "Select role", "label.select-website": "Select website", "label.session": "Session", "label.sessions": "Sessions", "label.settings": "Settings", "label.share-url": "Share URL", "label.single-day": "Single day", "label.start-step": "Start Step", "label.steps": "Steps", "label.sum": "Sum", "label.tablet": "Tablet", "label.team": "Team", "label.team-id": "Team ID", "label.team-manager": "Team manager", "label.team-member": "Team member", "label.team-name": "Team name", "label.team-owner": "Team owner", "label.team-view-only": "Team view only", "label.team-websites": "Team websites", "label.teams": "Teams", "label.theme": "Theme", "label.this-month": "This month", "label.this-week": "This week", "label.this-year": "This year", "label.timezone": "Timezone", "label.title": "Title", "label.today": "Today", "label.toggle-charts": "Toggle charts", "label.total": "Total", "label.total-records": "Total records", "label.tracking-code": "Tracking code", "label.transactions": "Transactions", "label.transfer": "Transfer", "label.transfer-website": "Transfer website", "label.true": "True", "label.type": "Type", "label.unique": "Unique", "label.unique-visitors": "Unique visitors", "label.uniqueCustomers": "Unique Customers", "label.unknown": "Unknown", "label.untitled": "Untitled", "label.update": "Update", "label.url": "URL", "label.urls": "URLs", "label.user": "User", "label.user-property": "User Property", "label.username": "Username", "label.users": "Users", "label.utm": "UTM", "label.utm-description": "Track your campaigns through UTM parameters.", "label.value": "Value", "label.view": "View", "label.view-details": "View details", "label.view-only": "View only", "label.views": "Views", "label.views-per-visit": "Views per visit", "label.visit-duration": "Visit duration", "label.visitors": "Visitors", "label.visits": "Visits", "label.website": "Website", "label.website-id": "Website ID", "label.websites": "Websites", "label.window": "Window", "label.yesterday": "Yesterday", "message.action-confirmation": "Type {confirmation} in the box below to confirm.", "message.active-users": "{x} current {x, plural, one {visitor} other {visitors}}", "message.collected-data": "Collected data", "message.confirm-delete": "Are you sure you want to delete {target}?", "message.confirm-leave": "Are you sure you want to leave {target}?", "message.confirm-remove": "Are you sure you want to remove {target}?", "message.confirm-reset": "Are you sure you want to reset {target}'s statistics?", "message.delete-team-warning": "Deleting a team will also delete all team websites.", "message.delete-website-warning": "All associated data will be deleted as well.", "message.error": "Something went wrong.", "message.event-log": "{event} on {url}", "message.go-to-settings": "Go to settings", "message.incorrect-username-password": "Incorrect username/password.", "message.invalid-domain": "Invalid domain", "message.min-password-length": "Minimum length of {n} characters", "message.new-version-available": "A new version of Superlytics {version} is available!", "message.no-data-available": "No data available.", "message.no-event-data": "No event data is available.", "message.no-match-password": "Passwords don't match", "message.no-results-found": "No results were found.", "message.no-team-websites": "This team does not have any websites.", "message.no-teams": "You have not created any teams.", "message.no-users": "There are no users.", "message.no-websites-configured": "You don't have any websites configured.", "message.page-not-found": "Page not found.", "message.reset-website": "To reset this website, type {confirmation} in the box below to confirm.", "message.reset-website-warning": "All statistics for this website will be deleted, but your tracking code will remain intact.", "message.saved": "Saved successfully.", "message.share-url": "This is the publicly shared URL for {target}.", "message.team-already-member": "You are already a member of the team.", "message.team-not-found": "Team not found.", "message.team-websites-info": "Websites can be viewed by anyone on the team.", "message.tracking-code": "Tracking code", "message.transfer-team-website-to-user": "Transfer this website to your account?", "message.transfer-user-website-to-team": "Select the team to transfer this website to.", "message.transfer-website": "Transfer website ownership to your account or another team.", "message.triggered-event": "Triggered event", "message.user-deleted": "User deleted.", "message.viewed-page": "Viewed page", "message.visitor-log": "Visitor from {country} using {browser} on {os} {device}", "message.visitors-dropped-off": "Visitors dropped off"}