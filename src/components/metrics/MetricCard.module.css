.card {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 150px;
}

.card.compare .change {
  font-size: 16px;
  margin: 10px 0;
}

.card:first-child {
  padding-left: 0;
}

.card:last-child {
  border: 0;
}

.value {
  font-size: 36px;
  font-weight: 700;
  white-space: nowrap;
  color: var(--base900);
  line-height: 1.5;
}

.value.prev {
  color: var(--base800);
}

.label {
  font-weight: 700;
  white-space: nowrap;
  color: var(--base800);
}
