.container {
  display: flex;
  flex-direction: column;
  max-width: 100vw;
}

.calendars {
  display: flex;
  justify-content: center;
}

.calendars > div + div {
  margin-inline-start: 20px;
  padding-inline-start: 20px;
  border-inline-start: 1px solid var(--base300);
}

.filter {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

@media only screen and (max-width: 768px) {
  .calendars {
    flex-direction: column;
  }

  .calendars > div + div {
    padding: 0;
    margin-inline-start: 0;
    margin-top: 20px;
    border: 0;
  }
}
