.table {
  position: relative;
  display: grid;
  grid-template-rows: fit-content(100%) auto;
  overflow: hidden;
  flex: 1;
}

.body {
  position: relative;
  height: 100%;
  overflow: auto;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 40px;
}

.title {
  display: flex;
  font-weight: 600;
}

.metric {
  font-weight: 600;
  text-align: center;
  width: 100px;
}

.row {
  position: relative;
  height: 30px;
  line-height: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  overflow: hidden;
  border-radius: var(--border-radius);
}

.row:hover {
  background-color: var(--base75);
}

.label {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  flex: 2;
  padding-left: 10px;
}

.label a {
  color: inherit;
  text-decoration: none;
}

.label a:hover {
  color: var(--primary400);
}

.label:empty {
  color: #b3b3b3;
}

.label:empty:before {
  content: 'Unknown';
}

.value {
  display: flex;
  align-items: center;
  gap: 10px;
  text-align: end;
  margin-inline-end: 5px;
  font-weight: 600;
}

.percent {
  position: relative;
  width: 50px;
  color: var(--base600);
  border-inline-start: 1px solid var(--base600);
  padding-inline-start: 10px;
  z-index: 1;
}

.bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 30px;
  opacity: 0.1;
  background: var(--primary400);
  z-index: -1;
}

.empty {
  min-height: 200px;
}

@media only screen and (max-width: 992px) {
  .body {
    height: auto;
  }
}
