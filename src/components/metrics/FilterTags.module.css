.filters {
  display: flex;
  align-items: center;
  gap: 10px;
  background: var(--base75);
  padding: 10px 20px;
  border: 1px solid var(--base400);
  border-radius: var(--border-radius);
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.label {
  font-weight: 700;
}

.tag {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  background: var(--base50);
  border: 1px solid var(--base400);
  border-radius: var(--border-radius);
  box-shadow: 1px 1px 1px var(--base500);
  padding: 6px 14px;
  cursor: pointer;
}

.tag:hover {
  background: var(--base100);
}

.close {
  font-weight: 700;
  align-self: center;
  margin-left: auto;
}

.name,
.value {
  color: var(--base700);
  font-weight: 700;
}

.operator {
  text-transform: lowercase;
  font-weight: 900;
}

.icon {
  margin-left: 10px;
  padding: 2px;
  border-radius: 100%;
}

.icon:hover {
  background: var(--base200);
}
