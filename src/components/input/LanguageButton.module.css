.menu {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  padding: 10px;
  background: var(--base50);
  z-index: var(--z-index-popup);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  margin-inline-start: 10px;
}

.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 200px;
  border-radius: var(--border-radius);
  padding: 5px 10px;
}

.item:hover {
  background: var(--base75);
  cursor: pointer;
}

.selected {
  font-weight: 700;
  background: var(--blue100);
}

.icon {
  color: var(--primary400);
}

@media screen and (max-width: 992px) {
  .menu {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: 768px) {
  .menu {
    transform: translateX(40px);
  }
}
