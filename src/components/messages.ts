import { defineMessages } from 'react-intl';

export const labels = defineMessages({
  ok: { id: 'label.ok', defaultMessage: 'OK' },
  unknown: { id: 'label.unknown', defaultMessage: 'Unknown' },
  required: { id: 'label.required', defaultMessage: 'Required' },
  save: { id: 'label.save', defaultMessage: 'Save' },
  cancel: { id: 'label.cancel', defaultMessage: 'Cancel' },
  continue: { id: 'label.continue', defaultMessage: 'Continue' },
  delete: { id: 'label.delete', defaultMessage: 'Delete' },
  leave: { id: 'label.leave', defaultMessage: 'Leave' },
  users: { id: 'label.users', defaultMessage: 'Users' },
  createUser: { id: 'label.create-user', defaultMessage: 'Create user' },
  deleteUser: { id: 'label.delete-user', defaultMessage: 'Delete user' },
  username: { id: 'label.username', defaultMessage: 'Username' },
  password: { id: 'label.password', defaultMessage: 'Password' },
  role: { id: 'label.role', defaultMessage: 'Role' },
  user: { id: 'label.user', defaultMessage: 'User' },
  viewOnly: { id: 'label.view-only', defaultMessage: 'View only' },
  manage: { id: 'label.manage', defaultMessage: 'Manage' },
  admin: { id: 'label.admin', defaultMessage: 'Administrator' },
  confirm: { id: 'label.confirm', defaultMessage: 'Confirm' },
  details: { id: 'label.details', defaultMessage: 'Details' },
  website: { id: 'label.website', defaultMessage: 'Website' },
  websites: { id: 'label.websites', defaultMessage: 'Websites' },
  myWebsites: { id: 'label.my-websites', defaultMessage: 'My websites' },
  teamWebsites: { id: 'label.team-websites', defaultMessage: 'Team websites' },
  created: { id: 'label.created', defaultMessage: 'Created' },
  createdBy: { id: 'label.created-by', defaultMessage: 'Created By' },
  edit: { id: 'label.edit', defaultMessage: 'Edit' },
  name: { id: 'label.name', defaultMessage: 'Name' },
  manager: { id: 'label.manager', defaultMessage: 'Manager' },
  member: { id: 'label.member', defaultMessage: 'Member' },
  members: { id: 'label.members', defaultMessage: 'Members' },
  accessCode: { id: 'label.access-code', defaultMessage: 'Access code' },
  teamId: { id: 'label.team-id', defaultMessage: 'Team ID' },
  team: { id: 'label.team', defaultMessage: 'Team' },
  teamName: { id: 'label.team-name', defaultMessage: 'Team name' },
  regenerate: { id: 'label.regenerate', defaultMessage: 'Regenerate' },
  remove: { id: 'label.remove', defaultMessage: 'Remove' },
  join: { id: 'label.join', defaultMessage: 'Join' },
  createTeam: { id: 'label.create-team', defaultMessage: 'Create team' },
  joinTeam: { id: 'label.join-team', defaultMessage: 'Join team' },
  settings: { id: 'label.settings', defaultMessage: 'Settings' },
  owner: { id: 'label.owner', defaultMessage: 'Owner' },
  teamOwner: { id: 'label.team-owner', defaultMessage: 'Team owner' },
  teamManager: { id: 'label.team-manager', defaultMessage: 'Team manager' },
  teamMember: { id: 'label.team-member', defaultMessage: 'Team member' },
  teamViewOnly: { id: 'label.team-view-only', defaultMessage: 'Team view only' },
  enableShareUrl: { id: 'label.enable-share-url', defaultMessage: 'Enable share URL' },
  data: { id: 'label.data', defaultMessage: 'Data' },
  trackingCode: { id: 'label.tracking-code', defaultMessage: 'Tracking code' },
  shareUrl: { id: 'label.share-url', defaultMessage: 'Share URL' },
  actions: { id: 'label.actions', defaultMessage: 'Actions' },
  domain: { id: 'label.domain', defaultMessage: 'Domain' },
  websiteId: { id: 'label.website-id', defaultMessage: 'Website ID' },
  resetWebsite: { id: 'label.reset-website', defaultMessage: 'Reset website' },
  deleteWebsite: { id: 'label.delete-website', defaultMessage: 'Delete website' },
  transferWebsite: { id: 'label.transfer-website', defaultMessage: 'Transfer website' },
  deleteReport: { id: 'label.delete-report', defaultMessage: 'Delete report' },
  reset: { id: 'label.reset', defaultMessage: 'Reset' },
  addWebsite: { id: 'label.add-website', defaultMessage: 'Add website' },
  addMember: { id: 'label.add-member', defaultMessage: 'Add member' },
  editMember: { id: 'label.edit-member', defaultMessage: 'Edit member' },
  removeMember: { id: 'label.remove-member', defaultMessage: 'Remove member' },
  addDescription: { id: 'label.add-description', defaultMessage: 'Add description' },
  changePassword: { id: 'label.change-password', defaultMessage: 'Change password' },
  currentPassword: { id: 'label.current-password', defaultMessage: 'Current password' },
  newPassword: { id: 'label.new-password', defaultMessage: 'New password' },
  confirmPassword: { id: 'label.confirm-password', defaultMessage: 'Confirm password' },
  timezone: { id: 'label.timezone', defaultMessage: 'Timezone' },
  defaultDateRange: { id: 'label.default-date-range', defaultMessage: 'Default date range' },
  language: { id: 'label.language', defaultMessage: 'Language' },
  theme: { id: 'label.theme', defaultMessage: 'Theme' },
  profile: { id: 'label.profile', defaultMessage: 'Profile' },
  dashboard: { id: 'label.dashboard', defaultMessage: 'Dashboard' },
  more: { id: 'label.more', defaultMessage: 'More' },
  realtime: { id: 'label.realtime', defaultMessage: 'Realtime' },
  queries: { id: 'label.queries', defaultMessage: 'Queries' },
  teams: { id: 'label.teams', defaultMessage: 'Teams' },
  analytics: { id: 'label.analytics', defaultMessage: 'Analytics' },
  login: { id: 'label.login', defaultMessage: 'Login' },
  logout: { id: 'label.logout', defaultMessage: 'Logout' },
  singleDay: { id: 'label.single-day', defaultMessage: 'Single day' },
  dateRange: { id: 'label.date-range', defaultMessage: 'Date range' },
  viewDetails: { id: 'label.view-details', defaultMessage: 'View details' },
  deleteTeam: { id: 'label.delete-team', defaultMessage: 'Delete team' },
  leaveTeam: { id: 'label.leave-team', defaultMessage: 'Leave team' },
  refresh: { id: 'label.refresh', defaultMessage: 'Refresh' },
  pages: { id: 'label.pages', defaultMessage: 'Pages' },
  entry: { id: 'label.entry', defaultMessage: 'Entry path' },
  exit: { id: 'label.exit', defaultMessage: 'Exit path' },
  referrers: { id: 'label.referrers', defaultMessage: 'Referrers' },
  hosts: { id: 'label.hosts', defaultMessage: 'Hosts' },
  screens: { id: 'label.screens', defaultMessage: 'Screens' },
  browsers: { id: 'label.browsers', defaultMessage: 'Browsers' },
  os: { id: 'label.os', defaultMessage: 'OS' },
  devices: { id: 'label.devices', defaultMessage: 'Devices' },
  countries: { id: 'label.countries', defaultMessage: 'Countries' },
  languages: { id: 'label.languages', defaultMessage: 'Languages' },
  tags: { id: 'label.tags', defaultMessage: 'Tags' },
  count: { id: 'label.count', defaultMessage: 'Count' },
  average: { id: 'label.average', defaultMessage: 'Average' },
  sum: { id: 'label.sum', defaultMessage: 'Sum' },
  event: { id: 'label.event', defaultMessage: 'Event' },
  events: { id: 'label.events', defaultMessage: 'Events' },
  query: { id: 'label.query', defaultMessage: 'Query' },
  queryParameters: { id: 'label.query-parameters', defaultMessage: 'Query parameters' },
  back: { id: 'label.back', defaultMessage: 'Back' },
  visitors: { id: 'label.visitors', defaultMessage: 'Visitors' },
  visits: { id: 'label.visits', defaultMessage: 'Visits' },
  filterCombined: { id: 'label.filter-combined', defaultMessage: 'Combined' },
  filterRaw: { id: 'label.filter-raw', defaultMessage: 'Raw' },
  views: { id: 'label.views', defaultMessage: 'Views' },
  none: { id: 'label.none', defaultMessage: 'None' },
  clearAll: { id: 'label.clear-all', defaultMessage: 'Clear all' },
  property: { id: 'label.property', defaultMessage: 'Property' },
  today: { id: 'label.today', defaultMessage: 'Today' },
  lastHours: { id: 'label.last-hours', defaultMessage: 'Last {x} hours' },
  yesterday: { id: 'label.yesterday', defaultMessage: 'Yesterday' },
  thisWeek: { id: 'label.this-week', defaultMessage: 'This week' },
  lastDays: { id: 'label.last-days', defaultMessage: 'Last {x} days' },
  lastMonths: { id: 'label.last-months', defaultMessage: 'Last {x} months' },
  thisMonth: { id: 'label.this-month', defaultMessage: 'This month' },
  thisYear: { id: 'label.this-year', defaultMessage: 'This year' },
  allTime: { id: 'label.all-time', defaultMessage: 'All time' },
  customRange: { id: 'label.custom-range', defaultMessage: 'Custom range' },
  selectWebsite: { id: 'label.select-website', defaultMessage: 'Select website' },
  selectRole: { id: 'label.select-role', defaultMessage: 'Select role' },
  selectDate: { id: 'label.select-date', defaultMessage: 'Select date' },
  all: { id: 'label.all', defaultMessage: 'All' },
  session: { id: 'label.session', defaultMessage: 'Session' },
  sessions: { id: 'label.sessions', defaultMessage: 'Sessions' },
  distinctId: { id: 'label.distinct-id', defaultMessage: 'Distinct ID' },
  pageNotFound: { id: 'message.page-not-found', defaultMessage: 'Page not found' },
  activity: { id: 'label.activity', defaultMessage: 'Activity' },
  dismiss: { id: 'label.dismiss', defaultMessage: 'Dismiss' },
  poweredBy: { id: 'label.powered-by', defaultMessage: 'Powered by {name}' },
  pageViews: { id: 'label.page-views', defaultMessage: 'Page views' },
  uniqueVisitors: { id: 'label.unique-visitors', defaultMessage: 'Unique visitors' },
  bounceRate: { id: 'label.bounce-rate', defaultMessage: 'Bounce rate' },
  viewsPerVisit: { id: 'label.views-per-visit', defaultMessage: 'Views per visit' },
  visitDuration: { id: 'label.visit-duration', defaultMessage: 'Visit duration' },
  desktop: { id: 'label.desktop', defaultMessage: 'Desktop' },
  laptop: { id: 'label.laptop', defaultMessage: 'Laptop' },
  tablet: { id: 'label.tablet', defaultMessage: 'Tablet' },
  mobile: { id: 'label.mobile', defaultMessage: 'Mobile' },
  toggleCharts: { id: 'label.toggle-charts', defaultMessage: 'Toggle charts' },
  editDashboard: { id: 'label.edit-dashboard', defaultMessage: 'Edit dashboard' },
  title: { id: 'label.title', defaultMessage: 'Title' },
  view: { id: 'label.view', defaultMessage: 'View' },
  cities: { id: 'label.cities', defaultMessage: 'Cities' },
  regions: { id: 'label.regions', defaultMessage: 'Regions' },
  reports: { id: 'label.reports', defaultMessage: 'Reports' },
  eventData: { id: 'label.event-data', defaultMessage: 'Event data' },
  sessionData: { id: 'label.session-data', defaultMessage: 'Session data' },
  funnel: { id: 'label.funnel', defaultMessage: 'Funnel' },
  funnelDescription: {
    id: 'label.funnel-description',
    defaultMessage: 'Understand the conversion and drop-off rate of users.',
  },
  revenue: { id: 'label.revenue', defaultMessage: 'Revenue' },
  revenueDescription: {
    id: 'label.revenue-description',
    defaultMessage: 'Look into your revenue data and how users are spending.',
  },
  attribution: { id: 'label.attribution', defaultMessage: 'Attribution' },
  attributionDescription: {
    id: 'label.attribution-description',
    defaultMessage: 'See how users engage with your marketing and what drives conversions.',
  },
  currency: { id: 'label.currency', defaultMessage: 'Currency' },
  model: { id: 'label.model', defaultMessage: 'Model' },
  url: { id: 'label.url', defaultMessage: 'URL' },
  urls: { id: 'label.urls', defaultMessage: 'URLs' },
  path: { id: 'label.path', defaultMessage: 'Path' },
  paths: { id: 'label.paths', defaultMessage: 'Paths' },
  add: { id: 'label.add', defaultMessage: 'Add' },
  update: { id: 'label.update', defaultMessage: 'Update' },
  window: { id: 'label.window', defaultMessage: 'Window' },
  runQuery: { id: 'label.run-query', defaultMessage: 'Run query' },
  field: { id: 'label.field', defaultMessage: 'Field' },
  fields: { id: 'label.fields', defaultMessage: 'Fields' },
  createReport: { id: 'label.create-report', defaultMessage: 'Create report' },
  description: { id: 'label.description', defaultMessage: 'Description' },
  untitled: { id: 'label.untitled', defaultMessage: 'Untitled' },
  type: { id: 'label.type', defaultMessage: 'Type' },
  filter: { id: 'label.filter', defaultMessage: 'Filter' },
  filters: { id: 'label.filters', defaultMessage: 'Filters' },
  breakdown: { id: 'label.breakdown', defaultMessage: 'Breakdown' },
  true: { id: 'label.true', defaultMessage: 'True' },
  false: { id: 'label.false', defaultMessage: 'False' },
  is: { id: 'label.is', defaultMessage: 'Is' },
  isNot: { id: 'label.is-not', defaultMessage: 'Is not' },
  isSet: { id: 'label.is-set', defaultMessage: 'Is set' },
  isNotSet: { id: 'label.is-not-set', defaultMessage: 'Is not set' },
  greaterThan: { id: 'label.greater-than', defaultMessage: 'Greater than' },
  lessThan: { id: 'label.less-than', defaultMessage: 'Less than' },
  greaterThanEquals: { id: 'label.greater-than-equals', defaultMessage: 'Greater than or equals' },
  lessThanEquals: { id: 'label.less-than-equals', defaultMessage: 'Less than or equals' },
  contains: { id: 'label.contains', defaultMessage: 'Contains' },
  doesNotContain: { id: 'label.does-not-contain', defaultMessage: 'Does not contain' },
  before: { id: 'label.before', defaultMessage: 'Before' },
  after: { id: 'label.after', defaultMessage: 'After' },
  total: { id: 'label.total', defaultMessage: 'Total' },
  min: { id: 'label.min', defaultMessage: 'Min' },
  max: { id: 'label.max', defaultMessage: 'Max' },
  unique: { id: 'label.unique', defaultMessage: 'Unique' },
  value: { id: 'label.value', defaultMessage: 'Value' },
  overview: { id: 'label.overview', defaultMessage: 'Overview' },
  totalRecords: { id: 'label.total-records', defaultMessage: 'Total records' },
  insights: { id: 'label.insights', defaultMessage: 'Insights' },
  insightsDescription: {
    id: 'label.insights-description',
    defaultMessage: 'Dive deeper into your data by using segments and filters.',
  },
  retention: { id: 'label.retention', defaultMessage: 'Retention' },
  retentionDescription: {
    id: 'label.retention-description',
    defaultMessage: 'Measure your website stickiness by tracking how often users return.',
  },
  dropoff: { id: 'label.dropoff', defaultMessage: 'Dropoff' },
  referrer: { id: 'label.referrer', defaultMessage: 'Referrer' },
  host: { id: 'label.host', defaultMessage: 'Host' },
  country: { id: 'label.country', defaultMessage: 'Country' },
  region: { id: 'label.region', defaultMessage: 'Region' },
  city: { id: 'label.city', defaultMessage: 'City' },
  browser: { id: 'label.browser', defaultMessage: 'Browser' },
  device: { id: 'label.device', defaultMessage: 'Device' },
  pageTitle: { id: 'label.pageTitle', defaultMessage: 'Page title' },
  tag: { id: 'label.tag', defaultMessage: 'Tag' },
  day: { id: 'label.day', defaultMessage: 'Day' },
  date: { id: 'label.date', defaultMessage: 'Date' },
  pageOf: { id: 'label.page-of', defaultMessage: 'Page {current} of {total}' },
  create: { id: 'label.create', defaultMessage: 'Create' },
  search: { id: 'label.search', defaultMessage: 'Search' },
  numberOfRecords: {
    id: 'label.number-of-records',
    defaultMessage: '{x} {x, plural, one {record} other {records}}',
  },
  select: { id: 'label.select', defaultMessage: 'Select' },
  myAccount: { id: 'label.my-account', defaultMessage: 'My account' },
  transfer: { id: 'label.transfer', defaultMessage: 'Transfer' },
  transactions: { id: 'label.transactions', defaultMessage: 'Transactions' },
  uniqueCustomers: { id: 'label.uniqueCustomers', defaultMessage: 'Unique Customers' },
  viewedPage: {
    id: 'message.viewed-page',
    defaultMessage: 'Viewed page',
  },
  collectedData: {
    id: 'message.collected-data',
    defaultMessage: 'Collected data',
  },
  triggeredEvent: {
    id: 'message.triggered-event',
    defaultMessage: 'Triggered event',
  },
  visitorsDroppedOff: {
    id: 'message.visitors-dropped-off',
    defaultMessage: 'Visitors dropped off',
  },
  utm: { id: 'label.utm', defaultMessage: 'UTM' },
  utmDescription: {
    id: 'label.utm-description',
    defaultMessage: 'Track your campaigns through UTM parameters.',
  },
  conversionStep: { id: 'label.conversion-step', defaultMessage: 'Conversion Step' },
  steps: { id: 'label.steps', defaultMessage: 'Steps' },
  startStep: { id: 'label.start-step', defaultMessage: 'Start Step' },
  endStep: { id: 'label.end-step', defaultMessage: 'End Step' },
  addStep: { id: 'label.add-step', defaultMessage: 'Add step' },
  goal: { id: 'label.goal', defaultMessage: 'Goal' },
  goals: { id: 'label.goals', defaultMessage: 'Goals' },
  goalsDescription: {
    id: 'label.goals-description',
    defaultMessage: 'Track your goals for pageviews and events.',
  },
  journey: { id: 'label.journey', defaultMessage: 'Journey' },
  journeyDescription: {
    id: 'label.journey-description',
    defaultMessage: 'Understand how users navigate through your website.',
  },
  compare: { id: 'label.compare', defaultMessage: 'Compare' },
  current: { id: 'label.current', defaultMessage: 'Current' },
  previous: { id: 'label.previous', defaultMessage: 'Previous' },
  previousPeriod: { id: 'label.previous-period', defaultMessage: 'Previous period' },
  previousYear: { id: 'label.previous-year', defaultMessage: 'Previous year' },
  lastSeen: { id: 'label.last-seen', defaultMessage: 'Last seen' },
  firstSeen: { id: 'label.first-seen', defaultMessage: 'First seen' },
  properties: { id: 'label.properties', defaultMessage: 'Properties' },
  channels: { id: 'label.channels', defaultMessage: 'Channels' },
  sources: { id: 'label.sources', defaultMessage: 'Sources' },
  medium: { id: 'label.medium', defaultMessage: 'Medium' },
  campaigns: { id: 'label.campaigns', defaultMessage: 'Campaigns' },
  content: { id: 'label.content', defaultMessage: 'Content' },
  terms: { id: 'label.terms', defaultMessage: 'Terms' },
  direct: { id: 'label.direct', defaultMessage: 'Direct' },
  referral: { id: 'label.referral', defaultMessage: 'Referral' },
  affiliate: { id: 'label.affiliate', defaultMessage: 'Affiliate' },
  email: { id: 'label.email', defaultMessage: 'Email' },
  sms: { id: 'label.sms', defaultMessage: 'SMS' },
  organicSearch: { id: 'label.organic-search', defaultMessage: 'Organic search' },
  organicSocial: { id: 'label.organic-social', defaultMessage: 'Organic social' },
  organicShopping: { id: 'label.organic-shopping', defaultMessage: 'Organic shopping' },
  organicVideo: { id: 'label.organic-video', defaultMessage: 'Organic video' },
  paidAds: { id: 'label.paid-ads', defaultMessage: 'Paid ads' },
  paidSearch: { id: 'label.paid-search', defaultMessage: 'Paid search' },
  paidSocial: { id: 'label.paid-social', defaultMessage: 'Paid social' },
  paidShopping: { id: 'label.paid-shopping', defaultMessage: 'Paid shopping' },
  paidVideo: { id: 'label.paid-video', defaultMessage: 'Paid video' },
  grouped: { id: 'label.grouped', defaultMessage: 'Grouped' },
  other: { id: 'label.other', defaultMessage: 'Other' },
});

export const messages = defineMessages({
  error: { id: 'message.error', defaultMessage: 'Something went wrong.' },
  saved: { id: 'message.saved', defaultMessage: 'Saved.' },
  noUsers: { id: 'message.no-users', defaultMessage: 'There are no users.' },
  userDeleted: { id: 'message.user-deleted', defaultMessage: 'User deleted.' },
  noDataAvailable: { id: 'message.no-data-available', defaultMessage: 'No data available.' },
  confirmReset: {
    id: 'message.confirm-reset',
    defaultMessage: 'Are you sure you want to reset {target}?',
  },
  confirmDelete: {
    id: 'message.confirm-delete',
    defaultMessage: 'Are you sure you want to delete {target}?',
  },
  confirmRemove: {
    id: 'message.confirm-remove',
    defaultMessage: 'Are you sure you want to remove {target}?',
  },
  confirmLeave: {
    id: 'message.confirm-leave',
    defaultMessage: 'Are you sure you want to leave {target}?',
  },
  minPasswordLength: {
    id: 'message.min-password-length',
    defaultMessage: 'Minimum length of {n} characters',
  },
  noTeams: {
    id: 'message.no-teams',
    defaultMessage: 'You have not created any teams.',
  },
  shareUrl: {
    id: 'message.share-url',
    defaultMessage: 'Your website stats are publicly available at the following URL:',
  },
  trackingCode: {
    id: 'message.tracking-code',
    defaultMessage:
      'To track stats for this website, place the following code in the <head>...</head> section of your HTML.',
  },
  joinTeamWarning: {
    id: 'message.team-already-member',
    defaultMessage: 'You are already a member of the team.',
  },
  actionConfirmation: {
    id: 'message.action-confirmation',
    defaultMessage: 'Type {confirmation} in the box below to confirm.',
  },
  resetWebsite: {
    id: 'message.reset-website',
    defaultMessage: 'To reset this website, type {confirmation} in the box below to confirm.',
  },
  invalidDomain: {
    id: 'message.invalid-domain',
    defaultMessage: 'Invalid domain. Do not include http/https.',
  },
  resetWebsiteWarning: {
    id: 'message.reset-website-warning',
    defaultMessage:
      'All statistics for this website will be deleted, but your settings will remain intact.',
  },
  deleteWebsiteWarning: {
    id: 'message.delete-website-warning',
    defaultMessage: 'All website data will be deleted.',
  },
  deleteTeamWarning: {
    id: 'message.delete-team-warning',
    defaultMessage: 'Deleting a team will also delete all team websites.',
  },
  noResultsFound: {
    id: 'message.no-results-found',
    defaultMessage: 'No results found.',
  },
  noWebsitesConfigured: {
    id: 'message.no-websites-configured',
    defaultMessage: 'You do not have any websites configured.',
  },
  noTeamWebsites: {
    id: 'message.no-team-websites',
    defaultMessage: 'This team does not have any websites.',
  },
  teamWebsitesInfo: {
    id: 'message.team-websites-info',
    defaultMessage: 'Websites can be viewed by anyone on the team.',
  },
  noMatchPassword: { id: 'message.no-match-password', defaultMessage: 'Passwords do not match.' },
  goToSettings: {
    id: 'message.go-to-settings',
    defaultMessage: 'Go to settings',
  },
  activeUsers: {
    id: 'message.active-users',
    defaultMessage: '{x} current {x, plural, one {visitor} other {visitors}}',
  },
  teamNotFound: {
    id: 'message.team-not-found',
    defaultMessage: 'Team not found.',
  },
  visitorLog: {
    id: 'message.visitor-log',
    defaultMessage: 'Visitor from {country} using {browser} on {os} {device}',
  },
  eventLog: {
    id: 'message.event-log',
    defaultMessage: '{event} on {url}',
  },
  incorrectUsernamePassword: {
    id: 'message.incorrect-username-password',
    defaultMessage: 'Incorrect username and/or password.',
  },
  noEventData: {
    id: 'message.no-event-data',
    defaultMessage: 'No event data is available.',
  },
  newVersionAvailable: {
    id: 'message.new-version-available',
    defaultMessage: 'A new version of Superlytics {version} is available!',
  },
  transferWebsite: {
    id: 'message.transfer-website',
    defaultMessage: 'Transfer website ownership to your account or another team.',
  },
  transferTeamWebsiteToUser: {
    id: 'message.transfer-team-website-to-user',
    defaultMessage: 'Transfer this website to your account?',
  },
  transferUserWebsiteToTeam: {
    id: 'message.transfer-user-website-to-team',
    defaultMessage: 'Select the team to transfer this website to.',
  },
});
