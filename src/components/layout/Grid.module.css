.grid {
  display: grid;
  background: var(--base50);
}

.row {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  padding: 16px;
}

.row.compare {
  grid-template-columns: max-content 1fr 1fr;
}

.col {
  padding: 20px;
  min-height: 430px;
  background: var(--background);
  border: 1px solid var(--base300);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
}

.col:first-child {
  border: 1px solid var(--base300);
  border-radius: 12px;
  padding: 20px;
}

.col:last-child {
  border: 1px solid var(--base300);
  border-radius: 12px;
  padding: 20px;
}

.col.one {
  grid-column: span 6;
}

.col.two {
  grid-column: span 3;
}

.col.three {
  grid-column: span 2;
}

.col.two-one:first-child {
  grid-column: span 4;
}

.col.two-one:last-child {
  grid-column: span 2;
}

.col.one-two:first-child {
  grid-column: span 2;
}

.col.one-two:last-child {
  grid-column: span 4;
}

@media only screen and (max-width: 992px) {
  .row {
    gap: 16px;
    padding: 16px;
  }

  .row > .col {
    border: 1px solid var(--base300);
    border-radius: 12px;
    padding: 20px;
    background: var(--background);
    box-shadow: var(--card-shadow);
  }

  .col.two,
  .col.three,
  .col.one-two,
  .col.two-one {
    grid-column: span 6 !important;
  }
}
