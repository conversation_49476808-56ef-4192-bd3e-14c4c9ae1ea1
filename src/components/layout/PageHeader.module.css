.header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  align-content: center;
  align-self: stretch;
  flex-wrap: wrap;
  height: 100px;
}

.header a {
  color: var(--base600);
}

.header a:hover {
  color: var(--base900);
}

.title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 700;
  gap: 20px;
  height: 60px;
  flex: 1;
}

.breadcrumb {
  padding-top: 20px;
}

.icon {
  color: var(--base700);
  margin-inline-end: 1rem;
}

.actions {
  display: flex;
  justify-content: flex-end;
}

@media only screen and (max-width: 992px) {
  .header {
    margin-bottom: 10px;
  }
}
