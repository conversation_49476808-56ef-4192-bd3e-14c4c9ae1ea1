.notice {
  position: absolute;
  display: flex;
  justify-content: space-between;
  width: 100%;
  max-width: 800px;
  gap: 20px;
  margin: 60px auto;
  align-self: center;
  background: var(--base50);
  padding: 20px;
  border: 1px solid var(--base300);
  border-radius: var(--border-radius);
  z-index: 9999;
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

.message {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--font-color100);
  font-weight: 700;
}

.buttons {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  gap: 10px;
  flex: 0;
}

@media only screen and (max-width: 992px) {
  .message {
    height: 80px;
  }
}
