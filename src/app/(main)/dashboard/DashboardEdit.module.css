.buttons {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
}

.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 20px;
  border-radius: 5px;
  border: 1px solid var(--base400);
  background: var(--base50);
  margin-bottom: 10px;
}

.text {
  position: relative;
}

.name {
  font-weight: 600;
  font-size: 16px;
}

.domain {
  font-size: 14px;
  color: var(--base700);
}

.dragActive {
  cursor: grab;
}

.dragActive:active {
  cursor: grabbing;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 20px;
}

.search {
  max-width: 360px;
}

.active {
  border-color: var(--base600);
  box-shadow: 4px 4px 4px var(--base100);
}
