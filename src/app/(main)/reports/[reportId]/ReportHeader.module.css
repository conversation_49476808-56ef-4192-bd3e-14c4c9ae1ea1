.header {
  display: grid;
  grid-template-columns: 1fr min-content;
  align-items: center;
  grid-row: 1 / 2;
  grid-column: 1 / 3;
  margin: 20px 0 40px 0;
}

.title {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 24px;
  font-weight: 700;
  gap: 20px;
  height: 60px;
}

.type {
  font-size: 11px;
  font-weight: 700;
  text-transform: uppercase;
  color: var(--base600);
}

.description {
  color: var(--font-color300);
  max-width: 500px;
  height: 30px;
}

.actions {
  display: flex;
  align-items: center;
}
