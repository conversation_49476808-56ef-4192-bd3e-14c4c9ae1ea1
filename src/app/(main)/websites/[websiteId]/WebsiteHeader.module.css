.header {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
  padding: 20px 0px;
}

.title {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  font-size: 24px;
  font-weight: 700;
  overflow: hidden;
  height: 60px;
}

.actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  gap: 30px;
  min-height: 0;
  margin-left: auto;
}

.selected {
  font-weight: bold;
}

.links {
  display: flex;
  flex-direction: row;
  align-items: center;
}

@media only screen and (max-width: 768px) {
  .header {
    grid-template-columns: 1fr;
  }

  .links {
    justify-content: space-evenly;
    flex: 1;
    border-bottom: 1px solid var(--base300);
    padding-bottom: 10px;
    margin-bottom: 10px;
  }

  .label {
    display: none;
  }

  .icon,
  .icon svg {
    width: 20px;
    height: 20px;
  }
}
