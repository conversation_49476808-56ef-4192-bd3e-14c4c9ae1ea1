.layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  border-top: 1px solid var(--base300);
}

.menu {
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 20px 20px 20px 0;
}

.back {
  display: inline-flex;
  align-items: center;
  align-self: center;
  margin-bottom: 20px;
}

.content {
  min-height: 800px;
  padding: 20px 0 20px 20px;
  border-left: 1px solid var(--base300);
}

.dropdown {
  display: none;
}

@media screen and (max-width: 992px) {
  .layout {
    grid-template-columns: 1fr;
  }

  .content {
    border: 0;
  }

  .back {
    align-self: flex-start;
    margin: 0;
  }

  .nav {
    display: none;
  }

  .dropdown {
    display: flex;
    width: 200px;
    align-self: flex-end;
  }

  .menu {
    display: flex;
    flex-direction: row;
    gap: 20px;
    align-items: center;
    justify-content: space-between;
    padding-inline-end: 0;
    z-index: 10;
  }
}
