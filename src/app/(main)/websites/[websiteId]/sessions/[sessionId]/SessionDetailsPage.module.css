.page {
  display: grid;
  grid-template-columns: max-content 1fr max-content;
  margin-bottom: 40px;
  position: relative;
}

.sidebar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  width: 300px;
  padding-right: 20px;
  border-right: 1px solid var(--base300);
  position: relative;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 30px;
  padding: 0 20px;
  position: relative;
}

.data {
  width: 300px;
  border-left: 1px solid var(--base300);
  padding-left: 20px;
  position: relative;
  transition: width 200ms ease-in-out;
}

@media screen and (max-width: 992px) {
  .page {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .sidebar,
  .data {
    border: 0;
    width: auto;
  }
}
