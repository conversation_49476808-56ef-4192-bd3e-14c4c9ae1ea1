import { NextRequest, NextResponse } from 'next/server';
import { validateSession } from '@/lib/supabase/server';
import { syncUserToDatabase } from '@/lib/supabase/user-sync';
import { getUser } from '@/queries';
import type { SupabaseUser } from '@/lib/supabase/client';

export async function POST(request: NextRequest) {
  try {
    const session = await validateSession(request);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Sync user to local database
    const localUser = await syncUserToDatabase(session.user as SupabaseUser);

    // Get full user data
    const fullUser = await getUser(localUser.id);

    if (!fullUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({
      user: {
        id: fullUser.id,
        username: fullUser.username || fullUser.email?.split('@')[0] || '',
        email: fullUser.email,
        role: fullUser.role,
        isAdmin: fullUser.role === 'admin',
        displayName: fullUser.displayName,
      },
      token: session.token,
    });
  } catch (error) {
    console.error('SSO error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
