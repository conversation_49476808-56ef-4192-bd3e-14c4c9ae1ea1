import {
  arSA,
  be,
  bn,
  bg,
  bs,
  cs,
  sk,
  da,
  de,
  el,
  enUS,
  enGB,
  es,
  fi,
  fr,
  faIR,
  he,
  hi,
  hr,
  id,
  it,
  ja,
  km,
  ko,
  lt,
  mn,
  ms,
  nb,
  nl,
  pl,
  pt,
  ptBR,
  ro,
  ru,
  sl,
  sv,
  ta,
  th,
  tr,
  uk,
  zhCN,
  zhTW,
  ca,
  hu,
  vi,
} from 'date-fns/locale';

export const languages = {
  'ar-SA': { label: 'العربية', dateLocale: arSA, dir: 'rtl' },
  'be-BY': { label: 'Беларуская', dateLocale: be },
  'bg-BG': { label: 'български език', dateLocale: bg },
  'bn-BD': { label: 'বাংলা', dateLocale: bn },
  'bs-BA': { label: 'Bosanski', dateLocale: bs },
  'ca-ES': { label: 'Català', dateLocale: ca },
  'cs-CZ': { label: 'Čeština', dateLocale: cs },
  'da-DK': { label: 'Dansk', dateLocale: da },
  'de-CH': { label: 'Schwiizerdütsch', dateLocale: de },
  'de-DE': { label: 'Deutsch', dateLocale: de },
  'el-GR': { label: 'Ελληνικά', dateLocale: el },
  'en-GB': { label: 'English (UK)', dateLocale: enGB },
  'en-US': { label: 'English (US)', dateLocale: enUS },
  'es-ES': { label: 'Español', dateLocale: es },
  'fa-IR': { label: 'فارسی', dateLocale: faIR, dir: 'rtl' },
  'fi-FI': { label: 'Suomi', dateLocale: fi },
  'fo-FO': { label: 'Føroyskt' },
  'fr-FR': { label: 'Français', dateLocale: fr },
  'ga-ES': { label: 'Galacian (Spain)', dateLocale: es },
  'he-IL': { label: 'עברית', dateLocale: he },
  'hi-IN': { label: 'हिन्दी', dateLocale: hi },
  'hr-HR': { label: 'Hrvatski', dateLocale: hr },
  'hu-HU': { label: 'Hungarian', dateLocale: hu },
  'id-ID': { label: 'Bahasa Indonesia', dateLocale: id },
  'it-IT': { label: 'Italiano', dateLocale: it },
  'ja-JP': { label: '日本語', dateLocale: ja },
  'km-KH': { label: 'ភាសាខ្មែរ', dateLocale: km },
  'ko-KR': { label: '한국어', dateLocale: ko },
  'lt-LT': { label: 'Lietuvių', dateLocale: lt },
  'mn-MN': { label: 'Монгол', dateLocale: mn },
  'ms-MY': { label: 'Malay', dateLocale: ms },
  'my-MM': { label: 'မြန်မာဘာသာ', dateLocale: enUS },
  'nl-NL': { label: 'Nederlands', dateLocale: nl },
  'nb-NO': { label: 'Norsk Bokmål', dateLocale: nb },
  'pl-PL': { label: 'Polski', dateLocale: pl },
  'pt-BR': { label: 'Português do Brasil', dateLocale: ptBR },
  'pt-PT': { label: 'Português', dateLocale: pt },
  'ro-RO': { label: 'Română', dateLocale: ro },
  'ru-RU': { label: 'Русский', dateLocale: ru },
  'si-LK': { label: 'සිංහල', dateLocale: id },
  'sk-SK': { label: 'Slovenčina', dateLocale: sk },
  'sl-SI': { label: 'Slovenščina', dateLocale: sl },
  'sv-SE': { label: 'Svenska', dateLocale: sv },
  'ta-IN': { label: 'தமிழ்', dateLocale: ta },
  'th-TH': { label: 'ภาษาไทย', dateLocale: th },
  'tr-TR': { label: 'Türkçe', dateLocale: tr },
  'uk-UA': { label: 'українська', dateLocale: uk },
  'ur-PK': { label: 'Urdu (Pakistan)', dateLocale: uk, dir: 'rtl' },
  'vi-VN': { label: 'Tiếng Việt', dateLocale: vi },
  'zh-CN': { label: '中文', dateLocale: zhCN },
  'zh-TW': { label: '中文(繁體)', dateLocale: zhTW },
};

export function getDateLocale(locale: string) {
  return languages[locale]?.dateLocale || enUS;
}

export function getTextDirection(locale: string) {
  return languages[locale]?.dir || 'ltr';
}
