import { supabase<PERSON>dmin } from './server';
import { createUser, getUser, updateUser } from '@/queries';
import { uuid } from '@/lib/crypto';
import { ROLES } from '@/lib/constants';
import type { SupabaseUser } from './client';

// Sync Supabase Auth user to local database
export async function syncUserToDatabase(supabaseUser: SupabaseUser) {
  try {
    // Check if user already exists in our database
    let localUser = await getUser(supabaseUser.id);

    const userData = {
      id: supabaseUser.id,
      email: supabaseUser.email || null,
      username: supabaseUser.user_metadata?.username || supabaseUser.email?.split('@')[0] || null,
      displayName: supabaseUser.user_metadata?.full_name || null,
      supabaseId: supabaseUser.id,
      lastLoginAt: new Date(),
    };

    if (!localUser) {
      // Create new user in local database
      localUser = await createUser({
        ...userData,
        role: ROLES.user, // Default role for new users
      });
      console.log('Created new user in local database:', localUser.id);
    } else {
      // Update existing user
      localUser = await updateUser(localUser.id, {
        email: userData.email,
        username: userData.username,
        displayName: userData.displayName,
        lastLoginAt: userData.lastLoginAt,
        isActive: true,
      });
      console.log('Updated existing user in local database:', localUser.id);
    }

    return localUser;
  } catch (error) {
    console.error('Error syncing user to database:', error);
    throw error;
  }
}

// Get user from local database by Supabase ID
export async function getUserBySupabaseId(supabaseId: string) {
  try {
    // We'll need to add this query function
    return await getUser(supabaseId);
  } catch (error) {
    console.error('Error getting user by Supabase ID:', error);
    return null;
  }
}

// Update user role (admin function)
export async function updateUserRole(userId: string, role: string) {
  try {
    const user = await updateUser(userId, { role });

    // Also update Supabase Auth metadata
    await supabaseAdmin.auth.admin.updateUserById(userId, {
      app_metadata: { role },
    });

    return user;
  } catch (error) {
    console.error('Error updating user role:', error);
    throw error;
  }
}

// Deactivate user (soft delete)
export async function deactivateUser(userId: string) {
  try {
    const user = await updateUser(userId, {
      isActive: false,
      deletedAt: new Date(),
    });

    // Also disable in Supabase Auth
    await supabaseAdmin.auth.admin.updateUserById(userId, {
      ban_duration: 'none', // Permanently ban
    });

    return user;
  } catch (error) {
    console.error('Error deactivating user:', error);
    throw error;
  }
}

// Create admin user (for initial setup)
export async function createAdminUser(email: string, password: string) {
  try {
    // Create user in Supabase Auth
    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      app_metadata: { role: ROLES.admin },
    });

    if (authError || !authUser.user) {
      throw new Error(`Failed to create admin user in Supabase: ${authError?.message}`);
    }

    // Sync to local database
    const localUser = await syncUserToDatabase(authUser.user as SupabaseUser);

    // Update role to admin
    await updateUserRole(localUser.id, ROLES.admin);

    return localUser;
  } catch (error) {
    console.error('Error creating admin user:', error);
    throw error;
  }
}
