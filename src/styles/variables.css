html body {
  --primary200: #fce8e4;
  --primary300: #f8c5ba;
  --primary400: #f06449;
  --primary500: #e04a30;
  --primary600: #c93e26;
  --primary700: #b2331c;
  --primary800: #9b2912;
  --primary900: #841f08;

  --secondary: #ede6e3;
  --background: #ede6e3;
  --base50: #ede6e3;
  --base75: #e8e0dc;
  --base100: #e3dad5;
  --base300: #c9beb8;

  /* Override green colors for percentage indicators */
  --green700: #8dcdff;
  --green100: #e8f4ff;
  --green600: #8dcdff;

  /* Update disabled button colors */
  --disabled-background: #e3dad5;
  --disabled-color: #b3b3b3;

  /* Card shadows for light mode */
  --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  /* Rounded borders for buttons and components */
  --border-radius: 8px;
}

[data-theme='dark'] {
  --primary200: #fce8e4;
  --primary300: #f8c5ba;
  --primary400: #f87d64;
  --primary500: #f06449;
  --primary600: #e04a30;
  --primary700: #c93e26;
  --primary800: #b2331c;
  --primary900: #9b2912;

  --secondary: #232e2f;
  --background: #232e2f;
  --base50: #232e2f;
  --base75: #2c3738;
  --base100: #353f41;
  --base300: #4a5556;

  /* Override green colors for percentage indicators in dark mode */
  --green700: #8dcdff;
  --green100: #1a3a5c;
  --green600: #8dcdff;

  /* Update disabled button colors for dark mode */
  --disabled-background: #353f41;
  --disabled-color: #6e6e6e;

  /* Card shadows for dark mode */
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);

  /* Rounded borders for buttons and components */
  --border-radius: 8px;
}
