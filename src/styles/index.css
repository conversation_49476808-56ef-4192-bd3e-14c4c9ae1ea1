@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@300;400;500;600;700&display=swap');

html,
body {
  font-family:
    'DM Sans',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5rem;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  color: var(--font-color100);
  background: var(--base50);
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 400;
  line-height: 30px;
  padding: 0;
  margin: 0;
}

a,
a:active,
a:visited {
  color: var(--primary400);
  text-decoration: none;
}

p {
  line-height: 1.8rem;
}

main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

svg {
  shape-rendering: geometricPrecision;
}

#__next {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
  height: 100%;
}

::-webkit-scrollbar {
  width: 15px;
  background: var(--base50);
}

::-webkit-scrollbar-track {
  border: 7px solid rgba(0, 0, 0, 0);
  background-color: var(--base300);
  background-clip: padding-box;
}

body::-webkit-scrollbar-track,
main::-webkit-scrollbar-track {
  background-color: var(--base50);
}

::-webkit-scrollbar-thumb {
  border: 7px solid rgba(0, 0, 0, 0);
  background-color: var(--base600);
  background-clip: padding-box;
}

::-webkit-scrollbar-thumb:hover {
  border: 4px solid rgba(0, 0, 0, 0);
  background-color: var(--base800);
  background-clip: padding-box;
}

:root {
  --dark50: #111111;
  --dark75: #191919;
  --dark100: #222222;
  --dark150: #2a2a2a;
  --dark200: #313131;
  --dark300: #3a3a3a;
  --dark400: #484848;
  --dark500: #606060;
  --dark600: #6e6e6e;
  --dark700: #7b7b7b;
  --dark800: #b4b4b4;
  --dark900: #eeeeee;

  /* Custom brand colors */
  --superlytics-primary: #f06449;
  --superlytics-primary-light: #f87d64;
  --superlytics-primary-lighter: #f8c5ba;
  --superlytics-primary-dark: #e04a30;
  --superlytics-primary-darker: #c93e26;

  --superlytics-secondary: #ede6e3;
  --superlytics-background-light: #ede6e3;
  --superlytics-background-dark: #232e2f;
}
