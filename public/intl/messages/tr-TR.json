{"label.access-code": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.actions": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.activity": [{"type": 0, "value": "Aktivite Kaydı"}], "label.add": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.add-description": [{"type": 0, "value": "Açıklama ekle"}], "label.add-member": [{"type": 0, "value": "<PERSON><PERSON> e<PERSON>"}], "label.add-step": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.add-website": [{"type": 0, "value": "Web sitesi ekle"}], "label.admin": [{"type": 0, "value": "Administrator"}], "label.after": [{"type": 0, "value": "Sonra"}], "label.all": [{"type": 0, "value": "Tümü"}], "label.all-time": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.analytics": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.average": [{"type": 0, "value": "Ortalama"}], "label.back": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.before": [{"type": 0, "value": "Önce"}], "label.bounce-rate": [{"type": 0, "value": "Tek sayfa ziyaret oranı"}], "label.breakdown": [{"type": 0, "value": "Dağılım"}], "label.browser": [{"type": 0, "value": "Tarayıcı"}], "label.browsers": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.cancel": [{"type": 0, "value": "İptal"}], "label.change-password": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.cities": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.city": [{"type": 0, "value": "Şehir"}], "label.clear-all": [{"type": 0, "value": "<PERSON><PERSON><PERSON> temizle"}], "label.compare": [{"type": 0, "value": "Compare"}], "label.confirm": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.confirm-password": [{"type": 0, "value": "Parolayı onayla"}], "label.contains": [{"type": 0, "value": "İçeriği"}], "label.continue": [{"type": 0, "value": "<PERSON><PERSON> et"}], "label.count": [{"type": 0, "value": "Count"}], "label.countries": [{"type": 0, "value": "Ülkeler"}], "label.country": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.create": [{"type": 0, "value": "Oluştur"}], "label.create-report": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.create-team": [{"type": 0, "value": "Takım oluştur"}], "label.create-user": [{"type": 0, "value": "Kullanıcı oluştur"}], "label.created": [{"type": 0, "value": "<PERSON><PERSON>ş<PERSON><PERSON><PERSON>"}], "label.created-by": [{"type": 0, "value": "Tarafından oluşturldu"}], "label.current": [{"type": 0, "value": "Current"}], "label.current-password": [{"type": 0, "value": "Mevcut parola"}], "label.custom-range": [{"type": 0, "value": "Özelleştirilmiş aralık"}], "label.dashboard": [{"type": 0, "value": "Ko<PERSON>rol <PERSON>i"}], "label.data": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.date": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.date-range": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.day": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.default-date-range": [{"type": 0, "value": "Varsayılan tarih a<PERSON>ığı"}], "label.delete": [{"type": 0, "value": "Sil"}], "label.delete-report": [{"type": 0, "value": "<PERSON><PERSON> sil"}], "label.delete-team": [{"type": 0, "value": "Takım sil"}], "label.delete-user": [{"type": 0, "value": "Kullanıcı sil"}], "label.delete-website": [{"type": 0, "value": "Web sitesini sil"}], "label.description": [{"type": 0, "value": "<PERSON><PERSON>ı<PERSON><PERSON>"}], "label.desktop": [{"type": 0, "value": "Masaüstü"}], "label.details": [{"type": 0, "value": "Detaylar"}], "label.device": [{"type": 0, "value": "Cihaz"}], "label.devices": [{"type": 0, "value": "Cihazlar"}], "label.dismiss": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.does-not-contain": [{"type": 0, "value": "İçermez"}], "label.domain": [{"type": 0, "value": "<PERSON>"}], "label.dropoff": [{"type": 0, "value": "B<PERSON>rakma"}], "label.edit": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.edit-dashboard": [{"type": 0, "value": "Kontrol <PERSON>ini düzenle"}], "label.edit-member": [{"type": 0, "value": "Üyeyi düzenle"}], "label.enable-share-url": [{"type": 0, "value": "Anonim paylaşım URL'i aktif"}], "label.end-step": [{"type": 0, "value": "End Step"}], "label.entry": [{"type": 0, "value": "Entry URL"}], "label.event": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.event-data": [{"type": 0, "value": "<PERSON><PERSON> ve<PERSON>"}], "label.events": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.exit": [{"type": 0, "value": "Exit URL"}], "label.false": [{"type": 0, "value": "Yanlış"}], "label.field": [{"type": 0, "value": "<PERSON>"}], "label.fields": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.filter": [{"type": 0, "value": "Filtre"}], "label.filter-combined": [{"type": 0, "value": "Birleşik filtre"}], "label.filter-raw": [{"type": 0, "value": "Ham filtre"}], "label.filters": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.first-seen": [{"type": 0, "value": "First seen"}], "label.funnel": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.funnel-description": [{"type": 0, "value": "Kullanıcıların dönüşüm ve ayrılma oranlarını anlayın."}], "label.goal": [{"type": 0, "value": "Goal"}], "label.goals": [{"type": 0, "value": "Goals"}], "label.goals-description": [{"type": 0, "value": "Track your goals for pageviews and events."}], "label.greater-than": [{"type": 0, "value": "Büyüktür"}], "label.greater-than-equals": [{"type": 0, "value": "Büyük veya eşittir"}], "label.host": [{"type": 0, "value": "Host"}], "label.hosts": [{"type": 0, "value": "Hosts"}], "label.insights": [{"type": 0, "value": "Insights"}], "label.insights-description": [{"type": 0, "value": "Segmentleri ve filtreleri kullanarak verilerinizi derinlemesine inceleyin."}], "label.is": [{"type": 0, "value": "Is"}], "label.is-not": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.is-not-set": [{"type": 0, "value": "Ayarlanmamış"}], "label.is-set": [{"type": 0, "value": "Ayarlandı"}], "label.join": [{"type": 0, "value": "Katıl"}], "label.join-team": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> katıl"}], "label.journey": [{"type": 0, "value": "Journey"}], "label.journey-description": [{"type": 0, "value": "Understand how users navigate through your website."}], "label.language": [{"type": 0, "value": "Dil"}], "label.languages": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.laptop": [{"type": 0, "value": "Dizüstü"}], "label.last-days": [{"type": 0, "value": "Son "}, {"type": 1, "value": "x"}, {"type": 0, "value": " g<PERSON>n"}], "label.last-hours": [{"type": 0, "value": "Son "}, {"type": 1, "value": "x"}, {"type": 0, "value": " saat"}], "label.last-months": [{"type": 0, "value": "Son "}, {"type": 1, "value": "x"}, {"type": 0, "value": " ay"}], "label.last-seen": [{"type": 0, "value": "Last seen"}], "label.leave": [{"type": 0, "value": "Ayrıl"}], "label.leave-team": [{"type": 0, "value": "Takı<PERSON>dan <PERSON>"}], "label.less-than": [{"type": 0, "value": "Küçüktür"}], "label.less-than-equals": [{"type": 0, "value": "Küçük veya eşittir"}], "label.login": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.logout": [{"type": 0, "value": "Çıkış Yap"}], "label.manage": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.manager": [{"type": 0, "value": "Manager"}], "label.max": [{"type": 0, "value": "Max"}], "label.member": [{"type": 0, "value": "Üye"}], "label.members": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.min": [{"type": 0, "value": "Min"}], "label.mobile": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.more": [{"type": 0, "value": "Detaylı göster"}], "label.my-account": [{"type": 0, "value": "He<PERSON>b<PERSON>m"}], "label.my-websites": [{"type": 0, "value": "Web sitelerim"}], "label.name": [{"type": 0, "value": "İsim"}], "label.new-password": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.none": [{"type": 0, "value": "Yok"}], "label.number-of-records": [{"type": 1, "value": "x"}, {"type": 0, "value": " "}, {"offset": 0, "options": {"one": {"value": [{"type": 0, "value": "record"}]}, "other": {"value": [{"type": 0, "value": "records"}]}}, "pluralType": "cardinal", "type": 6, "value": "x"}], "label.ok": [{"type": 0, "value": "TAMAM"}], "label.os": [{"type": 0, "value": "OS"}], "label.overview": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.owner": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.page-of": [{"type": 1, "value": "total"}, {"type": 0, "value": " <PERSON><PERSON>da "}, {"type": 1, "value": "current"}, {"type": 0, "value": " "}], "label.page-views": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.pageTitle": [{"type": 0, "value": "Say<PERSON> başlığı"}], "label.pages": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.password": [{"type": 0, "value": "Pa<PERSON><PERSON>"}], "label.path": [{"type": 0, "value": "Path"}], "label.paths": [{"type": 0, "value": "Paths"}], "label.powered-by": [{"type": 0, "value": "Sağlayıcı: "}, {"type": 1, "value": "name"}], "label.previous": [{"type": 0, "value": "Previous"}], "label.previous-period": [{"type": 0, "value": "Previous period"}], "label.previous-year": [{"type": 0, "value": "Previous year"}], "label.profile": [{"type": 0, "value": "Profil"}], "label.properties": [{"type": 0, "value": "Properties"}], "label.property": [{"type": 0, "value": "Property"}], "label.queries": [{"type": 0, "value": "Sorg<PERSON>"}], "label.query": [{"type": 0, "value": "Sorg<PERSON>"}], "label.query-parameters": [{"type": 0, "value": "<PERSON>rg<PERSON> parametreleri"}], "label.realtime": [{"type": 0, "value": "Gerçek Zamanlı"}], "label.referrer": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.referrers": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.refresh": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.regenerate": [{"type": 0, "value": "Yeniden Oluştur"}], "label.region": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.regions": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.remove": [{"type": 0, "value": "Kaldır"}], "label.remove-member": [{"type": 0, "value": "Üyeyi kaldır"}], "label.reports": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.required": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> alan"}], "label.reset": [{"type": 0, "value": "Sıfırla"}], "label.reset-website": [{"type": 0, "value": "İstatistikleri sıfırla"}], "label.retention": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.retention-description": [{"type": 0, "value": "Kullanıcıların ne sıklıkla geri döndüğünü takip ederek web sitenizin kalıcılığını ölçün."}], "label.revenue": [{"type": 0, "value": "Revenue"}], "label.revenue-description": [{"type": 0, "value": "Look into your revenue across time."}], "label.revenue-property": [{"type": 0, "value": "Revenue Property"}], "label.role": [{"type": 0, "value": "Rol"}], "label.run-query": [{"type": 0, "value": "<PERSON><PERSON><PERSON>ı<PERSON>"}], "label.save": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.screens": [{"type": 0, "value": "Ekranlar"}], "label.search": [{"type": 0, "value": "Ara"}], "label.select": [{"type": 0, "value": "Seç"}], "label.select-date": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.select-role": [{"type": 0, "value": "Rol seç"}], "label.select-website": [{"type": 0, "value": "Web sitesi seç"}], "label.session": [{"type": 0, "value": "Session"}], "label.sessions": [{"type": 0, "value": "Sessions"}], "label.settings": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.share-url": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON> ad<PERSON>"}], "label.single-day": [{"type": 0, "value": "<PERSON><PERSON><PERSON> g<PERSON>n"}], "label.start-step": [{"type": 0, "value": "Start Step"}], "label.steps": [{"type": 0, "value": "<PERSON><PERSON><PERSON>lar"}], "label.sum": [{"type": 0, "value": "Toplam"}], "label.tablet": [{"type": 0, "value": "Tablet"}], "label.team": [{"type": 0, "value": "Takım"}], "label.team-id": [{"type": 0, "value": "Takım ID"}], "label.team-manager": [{"type": 0, "value": "Team manager"}], "label.team-member": [{"type": 0, "value": "Takım ü<PERSON>i"}], "label.team-name": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> ismi"}], "label.team-owner": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> sahibi"}], "label.team-view-only": [{"type": 0, "value": "Yalnızca ekip görünümü"}], "label.team-websites": [{"type": 0, "value": "Takım web siteleri"}], "label.teams": [{"type": 0, "value": "Takımlar"}], "label.theme": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.this-month": [{"type": 0, "value": "<PERSON>u ay"}], "label.this-week": [{"type": 0, "value": "Bu hafta"}], "label.this-year": [{"type": 0, "value": "<PERSON><PERSON> yıl"}], "label.timezone": [{"type": 0, "value": "Zaman <PERSON>"}], "label.title": [{"type": 0, "value": "Başlık"}], "label.today": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.toggle-charts": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.total": [{"type": 0, "value": "Toplam"}], "label.total-records": [{"type": 0, "value": "Toplam kayıt"}], "label.tracking-code": [{"type": 0, "value": "<PERSON>zleme kodu"}], "label.transactions": [{"type": 0, "value": "Transactions"}], "label.transfer": [{"type": 0, "value": "Transfer"}], "label.transfer-website": [{"type": 0, "value": "Transfer web sitesi"}], "label.true": [{"type": 0, "value": "Do<PERSON><PERSON>"}], "label.type": [{"type": 0, "value": "Tip"}], "label.unique": [{"type": 0, "value": "Benzersiz"}], "label.unique-visitors": [{"type": 0, "value": "Tekil kullanıcı"}], "label.uniqueCustomers": [{"type": 0, "value": "Unique Customers"}], "label.unknown": [{"type": 0, "value": "Bilinmeyen"}], "label.untitled": [{"type": 0, "value": "İsimsiz"}], "label.update": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.url": [{"type": 0, "value": "URL"}], "label.urls": [{"type": 0, "value": "URLs"}], "label.user": [{"type": 0, "value": "Kullanıcı"}], "label.user-property": [{"type": 0, "value": "User Property"}], "label.username": [{"type": 0, "value": "Kullanıcı adı"}], "label.users": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.utm": [{"type": 0, "value": "UTM"}], "label.utm-description": [{"type": 0, "value": "Kampanyalarınızı UTM parametreleri aracılığıyla takip edin."}], "label.value": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.view": [{"type": 0, "value": "G<PERSON>rü<PERSON><PERSON><PERSON>"}], "label.view-details": [{"type": 0, "value": "Detayı incele"}], "label.view-only": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.views": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.views-per-visit": [{"type": 0, "value": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON>"}], "label.visit-duration": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.visitors": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.visits": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.website": [{"type": 0, "value": "Web sitesi"}], "label.website-id": [{"type": 0, "value": "Website ID"}], "label.websites": [{"type": 0, "value": "Web siteleri"}], "label.window": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.yesterday": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "message.action-confirmation": [{"type": 0, "value": "Onaylamak için a<PERSON> kutuya "}, {"type": 1, "value": "confirmation"}, {"type": 0, "value": " yazın."}], "message.active-users": [{"type": 1, "value": "x"}, {"type": 0, "value": " akt<PERSON>"}], "message.collected-data": [{"type": 0, "value": "Collected data"}], "message.confirm-delete": [{"type": 1, "value": "target"}, {"type": 0, "value": " kaydını silmek istediğinizden emin misiniz?"}], "message.confirm-leave": [{"type": 1, "value": "target"}, {"type": 0, "value": " kaydından ayrılmak istediğinizden emin misiniz?"}], "message.confirm-remove": [{"type": 1, "value": "target"}, {"type": 0, "value": " kaydını kaldırmak istediğinizden emin misiniz?"}], "message.confirm-reset": [{"type": 1, "value": "target"}, {"type": 0, "value": " istatistiklerini sıfırlamak istediğinizden emin misiniz?"}], "message.delete-team-warning": [{"type": 0, "value": "Bir takımı silmek tüm takım web sitelerini de silecektir."}], "message.delete-website-warning": [{"type": 0, "value": "İlişkili tüm veriler de silinecektir."}], "message.error": [{"type": 0, "value": "<PERSON>ir <PERSON>ler ters gitti!"}], "message.event-log": [{"type": 1, "value": "event"}, {"type": 0, "value": " on "}, {"type": 1, "value": "url"}], "message.go-to-settings": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> git"}], "message.incorrect-username-password": [{"type": 0, "value": "Hatalı kullanıcı adı ya da parola."}], "message.invalid-domain": [{"type": 0, "value": "Geçersiz alan adı"}], "message.min-password-length": [{"type": 0, "value": "Minimum "}, {"type": 1, "value": "n"}, {"type": 0, "value": " karak<PERSON>"}], "message.new-version-available": [{"type": 0, "value": "<PERSON>ni versiyon Superlytics "}, {"type": 1, "value": "version"}, {"type": 0, "value": " mevcut!"}], "message.no-data-available": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> hiç veri yok."}], "message.no-event-data": [{"type": 0, "value": "<PERSON><PERSON><PERSON> olay verisi mevcut değ<PERSON>."}], "message.no-match-password": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "message.no-results-found": [{"type": 0, "value": "<PERSON>ç<PERSON> sonuç bulunamadı."}], "message.no-team-websites": [{"type": 0, "value": "<PERSON>u takımın herhangi bir web sitesi yok."}], "message.no-teams": [{"type": 0, "value": "Herhangi bir takım oluşturmadınız."}], "message.no-users": [{"type": 0, "value": "Kullanıcı yok."}], "message.no-websites-configured": [{"type": 0, "value": "Henüz hiç web sitesi tanımlamadınız"}], "message.page-not-found": [{"type": 0, "value": "Sayfa bulunamadı."}], "message.reset-website": [{"type": 0, "value": "Bu websitesini sıfılamak için aşağıdaki kutuya "}, {"type": 1, "value": "confirmation"}, {"type": 0, "value": " yazın."}], "message.reset-website-warning": [{"type": 0, "value": "Bu web sitesi için tüm istatistikler silinecek, ancak izleme kodunuz bozulmadan kalacaktır."}], "message.saved": [{"type": 0, "value": "Başarıyla kaydedildi."}], "message.share-url": [{"type": 1, "value": "target"}, {"type": 0, "value": " <PERSON><PERSON><PERSON> k<PERSON>bilir anonim paylaşım adresidir."}], "message.team-already-member": [{"type": 0, "value": "Zaten bu takımın üyesisiniz"}], "message.team-not-found": [{"type": 0, "value": "Takım bulunamadı"}], "message.team-websites-info": [{"type": 0, "value": "Web siteleri takımdaki herkes tarafından görüntülenebilir."}], "message.tracking-code": [{"type": 0, "value": "<PERSON>zleme kodu"}], "message.transfer-team-website-to-user": [{"type": 0, "value": "Bu web sitesi hesbınıza aktarılsın mı?"}], "message.transfer-user-website-to-team": [{"type": 0, "value": "Bu web sitesinin aktarılacağı takımı seçin."}], "message.transfer-website": [{"type": 0, "value": "Web sitesi sahipliğini hesabınıza veya başka bir takıma aktarın"}], "message.triggered-event": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> olay"}], "message.user-deleted": [{"type": 0, "value": "Kullan<PERSON><PERSON><PERSON> silindi."}], "message.viewed-page": [{"type": 0, "value": "Görüntülenen sayfa"}], "message.visitor-log": [{"type": 0, "value": "<PERSON><PERSON>: "}, {"type": 1, "value": "country"}, {"type": 0, "value": ", "}, {"type": 1, "value": "os"}, {"type": 0, "value": ", "}, {"type": 1, "value": "device"}, {"type": 0, "value": ", "}, {"type": 1, "value": "browser"}], "message.visitors-dropped-off": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}]}