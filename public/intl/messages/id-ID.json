{"label.access-code": [{"type": 0, "value": "<PERSON><PERSON> a<PERSON>"}], "label.actions": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.activity": [{"type": 0, "value": "Catatan aktivitas"}], "label.add": [{"type": 0, "value": "Tambah"}], "label.add-description": [{"type": 0, "value": "Tambah deskripsi"}], "label.add-member": [{"type": 0, "value": "Tambah anggota"}], "label.add-step": [{"type": 0, "value": "Tambah langkah"}], "label.add-website": [{"type": 0, "value": "Tambah situs web"}], "label.admin": [{"type": 0, "value": "Pen<PERSON><PERSON>"}], "label.after": [{"type": 0, "value": "Setela<PERSON>"}], "label.all": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.all-time": [{"type": 0, "value": "<PERSON><PERSON><PERSON> waktu"}], "label.analytics": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.average": [{"type": 0, "value": "<PERSON>a-rata"}], "label.back": [{"type": 0, "value": "Kembali"}], "label.before": [{"type": 0, "value": "Sebelum"}], "label.bounce-rate": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.breakdown": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.browser": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.browsers": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.cancel": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.change-password": [{"type": 0, "value": "Ganti kata sandi"}], "label.cities": [{"type": 0, "value": "Kota"}], "label.city": [{"type": 0, "value": "Kota"}], "label.clear-all": [{"type": 0, "value": "<PERSON><PERSON> semua"}], "label.compare": [{"type": 0, "value": "Bandingkan"}], "label.confirm": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.confirm-password": [{"type": 0, "value": "Konfirmasi kata sandi"}], "label.contains": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.continue": [{"type": 0, "value": "Lanjutkan"}], "label.count": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.countries": [{"type": 0, "value": "Negara"}], "label.country": [{"type": 0, "value": "Negara"}], "label.create": [{"type": 0, "value": "Buat"}], "label.create-report": [{"type": 0, "value": "<PERSON><PERSON><PERSON> laporan"}], "label.create-team": [{"type": 0, "value": "<PERSON>uat tim"}], "label.create-user": [{"type": 0, "value": "B<PERSON>t pen<PERSON>una"}], "label.created": [{"type": 0, "value": "Dibuat"}], "label.created-by": [{"type": 0, "value": "Dibuat oleh"}], "label.current": [{"type": 0, "value": "Saat ini"}], "label.current-password": [{"type": 0, "value": "<PERSON>a sandi sekarang"}], "label.custom-range": [{"type": 0, "value": "<PERSON><PERSON><PERSON> k<PERSON>"}], "label.dashboard": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.data": [{"type": 0, "value": "Data"}], "label.date": [{"type": 0, "value": "Tanggal"}], "label.date-range": [{"type": 0, "value": "<PERSON><PERSON><PERSON> tanggal"}], "label.day": [{"type": 0, "value": "<PERSON>"}], "label.default-date-range": [{"type": 0, "value": "<PERSON><PERSON><PERSON> tanggal bawaan"}], "label.delete": [{"type": 0, "value": "Hapus"}], "label.delete-report": [{"type": 0, "value": "<PERSON><PERSON> lap<PERSON>"}], "label.delete-team": [{"type": 0, "value": "<PERSON><PERSON> tim"}], "label.delete-user": [{"type": 0, "value": "Hapus pen<PERSON>una"}], "label.delete-website": [{"type": 0, "value": "Hapus situs web"}], "label.description": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.desktop": [{"type": 0, "value": "Desktop"}], "label.details": [{"type": 0, "value": "Detail"}], "label.device": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.devices": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.dismiss": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.does-not-contain": [{"type": 0, "value": "Tidak mengandung"}], "label.domain": [{"type": 0, "value": "Domain"}], "label.dropoff": [{"type": 0, "value": "Penurunan"}], "label.edit": [{"type": 0, "value": "Sunting"}], "label.edit-dashboard": [{"type": 0, "value": "<PERSON><PERSON> dasbor"}], "label.edit-member": [{"type": 0, "value": "Sunting anggota"}], "label.enable-share-url": [{"type": 0, "value": "Aktifkan URL berbagi"}], "label.end-step": [{"type": 0, "value": "<PERSON><PERSON><PERSON> a<PERSON>"}], "label.entry": [{"type": 0, "value": "URL masuk"}], "label.event": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.event-data": [{"type": 0, "value": "Data peristiwa"}], "label.events": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.exit": [{"type": 0, "value": "Exit URL"}], "label.false": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.field": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.fields": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.filter": [{"type": 0, "value": "Filter"}], "label.filter-combined": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.filter-raw": [{"type": 0, "value": "Mentah"}], "label.filters": [{"type": 0, "value": "Filters"}], "label.first-seen": [{"type": 0, "value": "<PERSON><PERSON><PERSON> kali di<PERSON>hat"}], "label.funnel": [{"type": 0, "value": "Funnel"}], "label.funnel-description": [{"type": 0, "value": "<PERSON><PERSON>i tingkat konversi dan penurunan pengguna."}], "label.goal": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.goals": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.goals-description": [{"type": 0, "value": "Lacak tujuan Anda untuk tampilan halaman dan peristiwa."}], "label.greater-than": [{"type": 0, "value": "<PERSON><PERSON><PERSON> dari"}], "label.greater-than-equals": [{"type": 0, "value": "<PERSON><PERSON>h dari atau sama dengan"}], "label.host": [{"type": 0, "value": "Host"}], "label.hosts": [{"type": 0, "value": "Hosts"}], "label.insights": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.insights-description": [{"type": 0, "value": "Jelajahi data Anda lebih dalam dengan menggunakan segmen dan filter."}], "label.is": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.is-not": [{"type": 0, "value": "Buka<PERSON>"}], "label.is-not-set": [{"type": 0, "value": "Tidak diatur"}], "label.is-set": [{"type": 0, "value": "Diatur"}], "label.join": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.join-team": [{"type": 0, "value": "<PERSON><PERSON><PERSON> tim"}], "label.journey": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.journey-description": [{"type": 0, "value": "<PERSON><PERSON>i bagaimana pengguna menavigasi situs web Anda."}], "label.language": [{"type": 0, "value": "Bahasa"}], "label.languages": [{"type": 0, "value": "Bahasa"}], "label.laptop": [{"type": 0, "value": "Laptop"}], "label.last-days": [{"type": 1, "value": "x"}, {"type": 0, "value": " hari te<PERSON><PERSON>"}], "label.last-hours": [{"type": 1, "value": "x"}, {"type": 0, "value": " jam terakhir"}], "label.last-months": [{"type": 1, "value": "x"}, {"type": 0, "value": " bulan te<PERSON>"}], "label.last-seen": [{"type": 0, "value": "<PERSON><PERSON><PERSON> kali di<PERSON>"}], "label.leave": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.leave-team": [{"type": 0, "value": "<PERSON><PERSON><PERSON> dari tim"}], "label.less-than": [{"type": 0, "value": "<PERSON><PERSON> dari"}], "label.less-than-equals": [{"type": 0, "value": "<PERSON><PERSON> dari atau sama dengan"}], "label.login": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.logout": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.manage": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.manager": [{"type": 0, "value": "Pen<PERSON><PERSON>"}], "label.max": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.member": [{"type": 0, "value": "Anggota"}], "label.members": [{"type": 0, "value": "Anggota"}], "label.min": [{"type": 0, "value": "Min"}], "label.mobile": [{"type": 0, "value": "Ponsel"}], "label.more": [{"type": 0, "value": "<PERSON><PERSON><PERSON> banyak"}], "label.my-account": [{"type": 0, "value": "<PERSON><PERSON><PERSON> saya"}], "label.my-websites": [{"type": 0, "value": "Situs web saya"}], "label.name": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.new-password": [{"type": 0, "value": "Kata sandi baru"}], "label.none": [{"type": 0, "value": "Tidak ada"}], "label.number-of-records": [{"type": 1, "value": "x"}, {"type": 0, "value": " "}, {"offset": 0, "options": {"one": {"value": [{"type": 0, "value": "record"}]}, "other": {"value": [{"type": 0, "value": "records"}]}}, "pluralType": "cardinal", "type": 6, "value": "x"}], "label.ok": [{"type": 0, "value": "OK"}], "label.os": [{"type": 0, "value": "OS"}], "label.overview": [{"type": 0, "value": "<PERSON><PERSON><PERSON> umum"}], "label.owner": [{"type": 0, "value": "Pemilik"}], "label.page-of": [{"type": 0, "value": "<PERSON><PERSON> "}, {"type": 1, "value": "current"}, {"type": 0, "value": " dari "}, {"type": 1, "value": "total"}], "label.page-views": [{"type": 0, "value": "<PERSON><PERSON><PERSON> ha<PERSON>an"}], "label.pageTitle": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.pages": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.password": [{"type": 0, "value": "<PERSON>a sandi"}], "label.path": [{"type": 0, "value": "Path"}], "label.paths": [{"type": 0, "value": "Paths"}], "label.powered-by": [{"type": 0, "value": "<PERSON><PERSON><PERSON> o<PERSON>h "}, {"type": 1, "value": "name"}], "label.previous": [{"type": 0, "value": "Sebelumnya"}], "label.previous-period": [{"type": 0, "value": "Periode sebelumnya"}], "label.previous-year": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.profile": [{"type": 0, "value": "Profil"}], "label.properties": [{"type": 0, "value": "Properties"}], "label.property": [{"type": 0, "value": "Property"}], "label.queries": [{"type": 0, "value": "Queries"}], "label.query": [{"type": 0, "value": "Query"}], "label.query-parameters": [{"type": 0, "value": "Query parameters"}], "label.realtime": [{"type": 0, "value": "<PERSON><PERSON><PERSON> n<PERSON>"}], "label.referrer": [{"type": 0, "value": "Perujuk"}], "label.referrers": [{"type": 0, "value": "Perujuk"}], "label.refresh": [{"type": 0, "value": "Segarkan"}], "label.regenerate": [{"type": 0, "value": "<PERSON><PERSON><PERSON> ul<PERSON>"}], "label.region": [{"type": 0, "value": "Wilayah"}], "label.regions": [{"type": 0, "value": "Wilayah"}], "label.remove": [{"type": 0, "value": "Hapus"}], "label.remove-member": [{"type": 0, "value": "Ha<PERSON> anggota"}], "label.reports": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.required": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.reset": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.reset-website": [{"type": 0, "value": "<PERSON><PERSON> ulang statistik"}], "label.retention": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.retention-description": [{"type": 0, "value": "<PERSON><PERSON>r daya tarik situs web Anda dengan melacak seberapa sering pengguna kembali."}], "label.revenue": [{"type": 0, "value": "Pendapatan"}], "label.revenue-description": [{"type": 0, "value": "<PERSON><PERSON> pendapatan Anda seiring waktu."}], "label.revenue-property": [{"type": 0, "value": "Properti pendapatan"}], "label.role": [{"type": 0, "value": "Role"}], "label.run-query": [{"type": 0, "value": "Run query"}], "label.save": [{"type": 0, "value": "Simpan"}], "label.screens": [{"type": 0, "value": "<PERSON>ar"}], "label.search": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.select": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.select-date": [{"type": 0, "value": "<PERSON><PERSON><PERSON> tanggal"}], "label.select-role": [{"type": 0, "value": "<PERSON><PERSON><PERSON> role"}], "label.select-website": [{"type": 0, "value": "Pilih situs web"}], "label.session": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.sessions": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.settings": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.share-url": [{"type": 0, "value": "Bagikan URL"}], "label.single-day": [{"type": 0, "value": "Se<PERSON>"}], "label.start-step": [{"type": 0, "value": "<PERSON><PERSON><PERSON> awal"}], "label.steps": [{"type": 0, "value": "Lang<PERSON><PERSON>"}], "label.sum": [{"type": 0, "value": "Sum"}], "label.tablet": [{"type": 0, "value": "Tablet"}], "label.team": [{"type": 0, "value": "<PERSON>"}], "label.team-id": [{"type": 0, "value": "ID tim"}], "label.team-manager": [{"type": 0, "value": "<PERSON><PERSON><PERSON> tim"}], "label.team-member": [{"type": 0, "value": "<PERSON><PERSON><PERSON> tim"}], "label.team-name": [{"type": 0, "value": "<PERSON><PERSON> tim"}], "label.team-owner": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> tim"}], "label.team-view-only": [{"type": 0, "value": "Team view only"}], "label.team-websites": [{"type": 0, "value": "Situs web tim"}], "label.teams": [{"type": 0, "value": "<PERSON>"}], "label.theme": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.this-month": [{"type": 0, "value": "<PERSON><PERSON><PERSON> ini"}], "label.this-week": [{"type": 0, "value": "<PERSON><PERSON> ini"}], "label.this-year": [{"type": 0, "value": "<PERSON><PERSON> ini"}], "label.timezone": [{"type": 0, "value": "Zona waktu"}], "label.title": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.today": [{"type": 0, "value": "<PERSON> ini"}], "label.toggle-charts": [{"type": 0, "value": "<PERSON>uka grafik"}], "label.total": [{"type": 0, "value": "Total"}], "label.total-records": [{"type": 0, "value": "Total baris"}], "label.tracking-code": [{"type": 0, "value": "<PERSON><PERSON> la<PERSON>"}], "label.transactions": [{"type": 0, "value": "Transaksi"}], "label.transfer": [{"type": 0, "value": "Transfer"}], "label.transfer-website": [{"type": 0, "value": "Transfer situs web"}], "label.true": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.type": [{"type": 0, "value": "Tipe"}], "label.unique": [{"type": 0, "value": "Unik"}], "label.unique-visitors": [{"type": 0, "value": "Pengunjung unik"}], "label.uniqueCustomers": [{"type": 0, "value": "Kustomer unik"}], "label.unknown": [{"type": 0, "value": "Tidak diketahui"}], "label.untitled": [{"type": 0, "value": "<PERSON><PERSON> judul"}], "label.update": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.url": [{"type": 0, "value": "URL"}], "label.urls": [{"type": 0, "value": "URLs"}], "label.user": [{"type": 0, "value": "Pengguna"}], "label.user-property": [{"type": 0, "value": "User Property"}], "label.username": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.users": [{"type": 0, "value": "Pengguna"}], "label.utm": [{"type": 0, "value": "UTM"}], "label.utm-description": [{"type": 0, "value": "Lacak kampanye Anda melalui parameter UTM."}], "label.value": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.view": [{"type": 0, "value": "Lihat"}], "label.view-details": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.view-only": [{"type": 0, "value": "<PERSON><PERSON> me<PERSON>"}], "label.views": [{"type": 0, "value": "Tampilan"}], "label.views-per-visit": [{"type": 0, "value": "Tampilan per kunjungan"}], "label.visit-duration": [{"type": 0, "value": "<PERSON><PERSON><PERSON> kunjungan rata-rata"}], "label.visitors": [{"type": 0, "value": "Pengunjung"}], "label.visits": [{"type": 0, "value": "Kunjungan"}], "label.website": [{"type": 0, "value": "Situs web"}], "label.website-id": [{"type": 0, "value": "ID situs web"}], "label.websites": [{"type": 0, "value": "Situs web"}], "label.window": [{"type": 0, "value": "Window"}], "label.yesterday": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "message.action-confirmation": [{"type": 0, "value": "Ketik "}, {"type": 1, "value": "confirmation"}, {"type": 0, "value": " pada kotak di bawah untuk mengonfirmasi."}], "message.active-users": [{"type": 1, "value": "x"}, {"type": 0, "value": " pengunjung saat ini"}], "message.collected-data": [{"type": 0, "value": "Data dikumpulkan"}], "message.confirm-delete": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> kamu yakin ingin menghapus "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.confirm-leave": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin mening<PERSON> "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.confirm-remove": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.confirm-reset": [{"type": 0, "value": "<PERSON>a yakin ingin mengatur ulang statistik "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.delete-team-warning": [{"type": 0, "value": "Menghapus tim juga akan menghapus semua situs web yang terkait."}], "message.delete-website-warning": [{"type": 0, "value": "Semua data terkait juga akan di<PERSON>."}], "message.error": [{"type": 0, "value": "<PERSON> yang salah."}], "message.event-log": [{"type": 1, "value": "event"}, {"type": 0, "value": " on "}, {"type": 1, "value": "url"}], "message.go-to-settings": [{"type": 0, "value": "<PERSON>gi ke pengaturan"}], "message.incorrect-username-password": [{"type": 0, "value": "<PERSON>a pen<PERSON>una/kata sandi salah."}], "message.invalid-domain": [{"type": 0, "value": "Domain tidak valid"}], "message.min-password-length": [{"type": 0, "value": "Minimal "}, {"type": 1, "value": "n"}, {"type": 0, "value": " karakter"}], "message.new-version-available": [{"type": 0, "value": "Versi baru dari Superlytics "}, {"type": 1, "value": "version"}, {"type": 0, "value": " telah tersedia!"}], "message.no-data-available": [{"type": 0, "value": "Tidak ada data."}], "message.no-event-data": [{"type": 0, "value": "Tidak ada data peristiwa"}], "message.no-match-password": [{"type": 0, "value": "Kata sandi tidak cocok"}], "message.no-results-found": [{"type": 0, "value": "Tidak ada hasil yang di<PERSON>n."}], "message.no-team-websites": [{"type": 0, "value": "Tim ini tidak memiliki situs web."}], "message.no-teams": [{"type": 0, "value": "Anda belum membuat tim."}], "message.no-users": [{"type": 0, "value": "Tidak ada pengguna."}], "message.no-websites-configured": [{"type": 0, "value": "Anda tidak memiliki situs web yang dikonfigurasi."}], "message.page-not-found": [{"type": 0, "value": "Halaman tidak ditemukan."}], "message.reset-website": [{"type": 0, "value": "Untuk mengatur ulang situs web ini, ketik "}, {"type": 1, "value": "confirmation"}, {"type": 0, "value": " pada kotak di bawah untuk mengonfirmasi."}], "message.reset-website-warning": [{"type": 0, "value": "Semua statistik pada situs web ini akan dihapus, tetapi kode lacak akan tetap terpasang"}], "message.saved": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> disimpan."}], "message.share-url": [{"type": 0, "value": "Ini adalah URL yang dibagikan secara publik untuk "}, {"type": 1, "value": "target"}, {"type": 0, "value": "."}], "message.team-already-member": [{"type": 0, "value": "<PERSON>a sudah menjadi anggota tim ini."}], "message.team-not-found": [{"type": 0, "value": "<PERSON> t<PERSON>."}], "message.team-websites-info": [{"type": 0, "value": "Situs web dapat dilihat oleh semua anggota tim."}], "message.tracking-code": [{"type": 0, "value": "<PERSON><PERSON> la<PERSON>"}], "message.transfer-team-website-to-user": [{"type": 0, "value": "Transfer situs web ini ke akun <PERSON>a?"}], "message.transfer-user-website-to-team": [{"type": 0, "value": "<PERSON><PERSON>h tim tujuan untuk mentransfer situs web ini."}], "message.transfer-website": [{"type": 0, "value": "Transfer kepemilikan situs web ke akun Anda atau tim lain"}], "message.triggered-event": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "message.user-deleted": [{"type": 0, "value": "Pengguna telah dihapus."}], "message.viewed-page": [{"type": 0, "value": "<PERSON><PERSON>"}], "message.visitor-log": [{"type": 0, "value": "Pengunjung dari "}, {"type": 1, "value": "country"}, {"type": 0, "value": " <PERSON><PERSON> "}, {"type": 1, "value": "browser"}, {"type": 0, "value": " di "}, {"type": 1, "value": "device"}, {"type": 0, "value": " "}, {"type": 1, "value": "os"}], "message.visitors-dropped-off": [{"type": 0, "value": "Pengunjung yang meninggalkan situs web"}]}