{"label.access-code": [{"type": 0, "value": "Có<PERSON>"}], "label.actions": [{"type": 0, "value": "Ações do usuário"}], "label.activity": [{"type": 0, "value": "Registro de atividades"}], "label.add": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.add-description": [{"type": 0, "value": "Adicionar descri<PERSON>"}], "label.add-member": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> membro"}], "label.add-step": [{"type": 0, "value": "Adicionar etapa"}], "label.add-website": [{"type": 0, "value": "Adicionar site"}], "label.admin": [{"type": 0, "value": "Administrador"}], "label.after": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.all": [{"type": 0, "value": "Todos"}], "label.all-time": [{"type": 0, "value": "Todos os períodos"}], "label.analytics": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.average": [{"type": 0, "value": "Média"}], "label.back": [{"type": 0, "value": "Voltar"}], "label.before": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.bounce-rate": [{"type": 0, "value": "Taxa de rejeição"}], "label.breakdown": [{"type": 0, "value": "Detalhamento"}], "label.browser": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.browsers": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.cancel": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.change-password": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.cities": [{"type": 0, "value": "Cidades"}], "label.city": [{"type": 0, "value": "Cidade"}], "label.clear-all": [{"type": 0, "value": "<PERSON><PERSON> tudo"}], "label.compare": [{"type": 0, "value": "Compare"}], "label.confirm": [{"type": 0, "value": "Confirmar"}], "label.confirm-password": [{"type": 0, "value": "Confirmar <PERSON><PERSON><PERSON>"}], "label.contains": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.continue": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.count": [{"type": 0, "value": "Count"}], "label.countries": [{"type": 0, "value": "Países"}], "label.country": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.create": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.create-report": [{"type": 0, "value": "<PERSON><PERSON><PERSON> <PERSON>"}], "label.create-team": [{"type": 0, "value": "Criar equipe"}], "label.create-user": [{"type": 0, "value": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>"}], "label.created": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.created-by": [{"type": 0, "value": "<PERSON><PERSON><PERSON> por"}], "label.current": [{"type": 0, "value": "Current"}], "label.current-password": [{"type": 0, "value": "<PERSON><PERSON> atual"}], "label.custom-range": [{"type": 0, "value": "<PERSON><PERSON>do personalizado"}], "label.dashboard": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.data": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.date": [{"type": 0, "value": "Data"}], "label.date-range": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.day": [{"type": 0, "value": "<PERSON>a"}], "label.default-date-range": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.delete": [{"type": 0, "value": "Excluir"}], "label.delete-report": [{"type": 0, "value": "Excluir relatório"}], "label.delete-team": [{"type": 0, "value": "Excluir equipe"}], "label.delete-user": [{"type": 0, "value": "Excluir usuário"}], "label.delete-website": [{"type": 0, "value": "Excluir site"}], "label.description": [{"type": 0, "value": "Descrição"}], "label.desktop": [{"type": 0, "value": "Desktop"}], "label.details": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.device": [{"type": 0, "value": "Dispositivo"}], "label.devices": [{"type": 0, "value": "Dispositivos"}], "label.dismiss": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.does-not-contain": [{"type": 0, "value": "Não contém"}], "label.domain": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.dropoff": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.edit": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.edit-dashboard": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.edit-member": [{"type": 0, "value": "<PERSON><PERSON> membro"}], "label.enable-share-url": [{"type": 0, "value": "Ativar link para compartilhar"}], "label.end-step": [{"type": 0, "value": "End Step"}], "label.entry": [{"type": 0, "value": "Entry URL"}], "label.event": [{"type": 0, "value": "Evento"}], "label.event-data": [{"type": 0, "value": "Dados do evento"}], "label.events": [{"type": 0, "value": "Tipos de eventos"}], "label.exit": [{"type": 0, "value": "Exit URL"}], "label.false": [{"type": 0, "value": "Não"}], "label.field": [{"type": 0, "value": "Campo"}], "label.fields": [{"type": 0, "value": "Campos"}], "label.filter": [{"type": 0, "value": "Filtro"}], "label.filter-combined": [{"type": 0, "value": "Combinado"}], "label.filter-raw": [{"type": 0, "value": "Bru<PERSON>"}], "label.filters": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.first-seen": [{"type": 0, "value": "First seen"}], "label.funnel": [{"type": 0, "value": "Funil"}], "label.funnel-description": [{"type": 0, "value": "Entenda a taxa de conversão e abandono dos seus usuários."}], "label.goal": [{"type": 0, "value": "Goal"}], "label.goals": [{"type": 0, "value": "Goals"}], "label.goals-description": [{"type": 0, "value": "Track your goals for pageviews and events."}], "label.greater-than": [{"type": 0, "value": "<PERSON><PERSON> que"}], "label.greater-than-equals": [{"type": 0, "value": "<PERSON><PERSON> ou igual a"}], "label.host": [{"type": 0, "value": "Host"}], "label.hosts": [{"type": 0, "value": "Hosts"}], "label.insights": [{"type": 0, "value": "Insights"}], "label.insights-description": [{"type": 0, "value": "Explore seus dados em mais detalhes usando filtros"}], "label.is": [{"type": 0, "value": "É igual a"}], "label.is-not": [{"type": 0, "value": "Não é igual a"}], "label.is-not-set": [{"type": 0, "value": "<PERSON><PERSON> definido"}], "label.is-set": [{"type": 0, "value": "Definido"}], "label.join": [{"type": 0, "value": "Participar"}], "label.join-team": [{"type": 0, "value": "Participar da equipe"}], "label.journey": [{"type": 0, "value": "Journey"}], "label.journey-description": [{"type": 0, "value": "Understand how users navigate through your website."}], "label.language": [{"type": 0, "value": "Idioma"}], "label.languages": [{"type": 0, "value": "Idiomas"}], "label.laptop": [{"type": 0, "value": "Notebook"}], "label.last-days": [{"type": 0, "value": "Últimos "}, {"type": 1, "value": "x"}, {"type": 0, "value": " dias"}], "label.last-hours": [{"type": 0, "value": "Últimas "}, {"type": 1, "value": "x"}, {"type": 0, "value": " horas"}], "label.last-months": [{"type": 0, "value": "Últimos "}, {"type": 1, "value": "x"}, {"type": 0, "value": " meses"}], "label.last-seen": [{"type": 0, "value": "Last seen"}], "label.leave": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.leave-team": [{"type": 0, "value": "<PERSON><PERSON> da equipe"}], "label.less-than": [{"type": 0, "value": "<PERSON><PERSON> que"}], "label.less-than-equals": [{"type": 0, "value": "<PERSON>or ou igual a"}], "label.login": [{"type": 0, "value": "Entrar"}], "label.logout": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.manage": [{"type": 0, "value": "Gerenciar"}], "label.manager": [{"type": 0, "value": "Manager"}], "label.max": [{"type": 0, "value": "Máximo"}], "label.member": [{"type": 0, "value": "Membro"}], "label.members": [{"type": 0, "value": "Me<PERSON><PERSON>"}], "label.min": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.mobile": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.more": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.my-account": [{"type": 0, "value": "Minha conta"}], "label.my-websites": [{"type": 0, "value": "Meus sites"}], "label.name": [{"type": 0, "value": "Nome"}], "label.new-password": [{"type": 0, "value": "Nova senha"}], "label.none": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.number-of-records": [{"type": 1, "value": "x"}, {"type": 0, "value": " "}, {"offset": 0, "options": {"one": {"value": [{"type": 0, "value": "registro"}]}, "other": {"value": [{"type": 0, "value": "registros"}]}}, "pluralType": "cardinal", "type": 6, "value": "x"}], "label.ok": [{"type": 0, "value": "OK"}], "label.os": [{"type": 0, "value": "Sistema operacional"}], "label.overview": [{"type": 0, "value": "Visão geral"}], "label.owner": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.page-of": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> "}, {"type": 1, "value": "current"}, {"type": 0, "value": " de "}, {"type": 1, "value": "total"}], "label.page-views": [{"type": 0, "value": "Visualizações de página"}], "label.pageTitle": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.pages": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.password": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.path": [{"type": 0, "value": "Path"}], "label.paths": [{"type": 0, "value": "Paths"}], "label.powered-by": [{"type": 0, "value": "Desenvolvido por "}, {"type": 1, "value": "name"}], "label.previous": [{"type": 0, "value": "Previous"}], "label.previous-period": [{"type": 0, "value": "Previous period"}], "label.previous-year": [{"type": 0, "value": "Previous year"}], "label.profile": [{"type": 0, "value": "Perfil"}], "label.properties": [{"type": 0, "value": "Properties"}], "label.property": [{"type": 0, "value": "Property"}], "label.queries": [{"type": 0, "value": "Consultas"}], "label.query": [{"type": 0, "value": "Consulta"}], "label.query-parameters": [{"type": 0, "value": "Parâ<PERSON><PERSON> da consult<PERSON>"}], "label.realtime": [{"type": 0, "value": "Tempo real"}], "label.referrer": [{"type": 0, "value": "Referência"}], "label.referrers": [{"type": 0, "value": "Referências"}], "label.refresh": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.regenerate": [{"type": 0, "value": "Gerar novamente"}], "label.region": [{"type": 0, "value": "Estado"}], "label.regions": [{"type": 0, "value": "Estados"}], "label.remove": [{"type": 0, "value": "Remover"}], "label.remove-member": [{"type": 0, "value": "Remover membro"}], "label.reports": [{"type": 0, "value": "Relatórios"}], "label.required": [{"type": 0, "value": "Obrigatório"}], "label.reset": [{"type": 0, "value": "Redefinir"}], "label.reset-website": [{"type": 0, "value": "Redefinir dados"}], "label.retention": [{"type": 0, "value": "Retenção"}], "label.retention-description": [{"type": 0, "value": "Avalie a fidelidade dos seus usuários medindo a frequência com que eles retornam."}], "label.revenue": [{"type": 0, "value": "Revenue"}], "label.revenue-description": [{"type": 0, "value": "Look into your revenue across time."}], "label.revenue-property": [{"type": 0, "value": "Revenue Property"}], "label.role": [{"type": 0, "value": "Função"}], "label.run-query": [{"type": 0, "value": "Executar consulta"}], "label.save": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.screens": [{"type": 0, "value": "<PERSON><PERSON><PERSON> de tela"}], "label.search": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.select": [{"type": 0, "value": "Selecionar"}], "label.select-date": [{"type": 0, "value": "Selecionar data"}], "label.select-role": [{"type": 0, "value": "Selecionar função"}], "label.select-website": [{"type": 0, "value": "Selecionar site"}], "label.session": [{"type": 0, "value": "Session"}], "label.sessions": [{"type": 0, "value": "Sessões"}], "label.settings": [{"type": 0, "value": "Configurações"}], "label.share-url": [{"type": 0, "value": "Link para compartilhar"}], "label.single-day": [{"type": 0, "value": "Apenas um dia"}], "label.start-step": [{"type": 0, "value": "Start Step"}], "label.steps": [{"type": 0, "value": "Etapas"}], "label.sum": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.tablet": [{"type": 0, "value": "Tablet"}], "label.team": [{"type": 0, "value": "Equipe"}], "label.team-id": [{"type": 0, "value": "ID da equipe"}], "label.team-manager": [{"type": 0, "value": "Team manager"}], "label.team-member": [{"type": 0, "value": "Membro da equipe"}], "label.team-name": [{"type": 0, "value": "Nome da equipe"}], "label.team-owner": [{"type": 0, "value": "Propriet<PERSON><PERSON> da e<PERSON>pe"}], "label.team-view-only": [{"type": 0, "value": "Apenas visualização da equipe"}], "label.team-websites": [{"type": 0, "value": "Sites da equipe"}], "label.teams": [{"type": 0, "value": "Equipes"}], "label.theme": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.this-month": [{"type": 0, "value": "<PERSON><PERSON> mês"}], "label.this-week": [{"type": 0, "value": "<PERSON><PERSON> semana"}], "label.this-year": [{"type": 0, "value": "Este ano"}], "label.timezone": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.title": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.today": [{"type": 0, "value": "Hoje"}], "label.toggle-charts": [{"type": 0, "value": "Alternar g<PERSON>"}], "label.total": [{"type": 0, "value": "Total"}], "label.total-records": [{"type": 0, "value": "Total de registros"}], "label.tracking-code": [{"type": 0, "value": "Código de rastreamento"}], "label.transactions": [{"type": 0, "value": "Transactions"}], "label.transfer": [{"type": 0, "value": "Transferir"}], "label.transfer-website": [{"type": 0, "value": "Transferir site"}], "label.true": [{"type": 0, "value": "<PERSON>m"}], "label.type": [{"type": 0, "value": "Tipo"}], "label.unique": [{"type": 0, "value": "Únicos"}], "label.unique-visitors": [{"type": 0, "value": "Visitantes únicos"}], "label.uniqueCustomers": [{"type": 0, "value": "Unique Customers"}], "label.unknown": [{"type": 0, "value": "Desconhecido"}], "label.untitled": [{"type": 0, "value": "<PERSON><PERSON> tí<PERSON>lo"}], "label.update": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.url": [{"type": 0, "value": "URL"}], "label.urls": [{"type": 0, "value": "URLs"}], "label.user": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.user-property": [{"type": 0, "value": "User Property"}], "label.username": [{"type": 0, "value": "Nome de usuário"}], "label.users": [{"type": 0, "value": "Usuários"}], "label.utm": [{"type": 0, "value": "UTM"}], "label.utm-description": [{"type": 0, "value": "Acompanhe suas campanhas de publicidade através de parâmetros UTM."}], "label.value": [{"type": 0, "value": "Valor"}], "label.view": [{"type": 0, "value": "Visualizar"}], "label.view-details": [{"type": 0, "value": "Ver mais"}], "label.view-only": [{"type": 0, "value": "Somente visualização"}], "label.views": [{"type": 0, "value": "Visualizações"}], "label.views-per-visit": [{"type": 0, "value": "Visualizações por visita"}], "label.visit-duration": [{"type": 0, "value": "Tempo médio de visita"}], "label.visitors": [{"type": 0, "value": "Visitantes"}], "label.visits": [{"type": 0, "value": "Visitas"}], "label.website": [{"type": 0, "value": "Site"}], "label.website-id": [{"type": 0, "value": "ID do site"}], "label.websites": [{"type": 0, "value": "Sites"}], "label.window": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.yesterday": [{"type": 0, "value": "Ontem"}], "message.action-confirmation": [{"type": 0, "value": "<PERSON><PERSON>e "}, {"type": 1, "value": "confirmation"}, {"type": 0, "value": " na caixa abaixo para confirmar."}], "message.active-users": [{"type": 0, "value": " Atualmente "}, {"type": 1, "value": "x"}, {"type": 0, "value": " usuários ativos"}], "message.collected-data": [{"type": 0, "value": "Collected data"}], "message.confirm-delete": [{"type": 0, "value": "Tem certeza de que deseja excluir "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.confirm-leave": [{"type": 0, "value": "Tem certeza de que deseja sair de "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.confirm-remove": [{"type": 0, "value": "Tem certeza que deseja remover "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.confirm-reset": [{"type": 0, "value": "Tem certeza que deseja redefinir os dados de "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.delete-team-warning": [{"type": 0, "value": "Excluir a equipe também excluirá todos os sites da equipe."}], "message.delete-website-warning": [{"type": 0, "value": "Todos os dados relacionados serão excluídos."}], "message.error": [{"type": 0, "value": "Ocorreu um erro."}], "message.event-log": [{"type": 1, "value": "event"}, {"type": 0, "value": " em "}, {"type": 1, "value": "url"}], "message.go-to-settings": [{"type": 0, "value": "<PERSON><PERSON> <PERSON> as configura<PERSON><PERSON><PERSON>"}], "message.incorrect-username-password": [{"type": 0, "value": "Nome de usuário ou senha incorretos."}], "message.invalid-domain": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "message.min-password-length": [{"type": 0, "value": "A senha deve ter no mínimo "}, {"type": 1, "value": "n"}, {"type": 0, "value": " caracteres"}], "message.new-version-available": [{"type": 0, "value": "Uma nova versão "}, {"type": 1, "value": "version"}, {"type": 0, "value": " do Superlytics está disponível!"}], "message.no-data-available": [{"type": 0, "value": "Não há dados disponíveis."}], "message.no-event-data": [{"type": 0, "value": "Não há eventos disponíveis."}], "message.no-match-password": [{"type": 0, "value": "As senhas não coincidem."}], "message.no-results-found": [{"type": 0, "value": "Nenhum resultado encontrado."}], "message.no-team-websites": [{"type": 0, "value": "Esta equipe não possui sites."}], "message.no-teams": [{"type": 0, "value": "Você ainda não criou nenhuma equipe."}], "message.no-users": [{"type": 0, "value": "Não há usuários."}], "message.no-websites-configured": [{"type": 0, "value": "Você ainda não configurou nenhum site."}], "message.page-not-found": [{"type": 0, "value": "Página não encontrada."}], "message.reset-website": [{"type": 0, "value": "Se você tiver certeza de que deseja redefinir este site, digite "}, {"type": 1, "value": "confirmation"}, {"type": 0, "value": " na caixa de entrada abaixo para confirmar."}], "message.reset-website-warning": [{"type": 0, "value": "Todos os dados estatísticos deste site serão excluídos, mas seu código de rastreamento permanecerá o mesmo."}], "message.saved": [{"type": 0, "value": "Salvo com sucesso."}], "message.share-url": [{"type": 0, "value": "Este é o link para compartilhar "}, {"type": 1, "value": "target"}, {"type": 0, "value": "."}], "message.team-already-member": [{"type": 0, "value": "Você já é membro desta equipe."}], "message.team-not-found": [{"type": 0, "value": "Equipe não encontrada."}], "message.team-websites-info": [{"type": 0, "value": "Qualquer membro da equipe pode visualizar os sites."}], "message.tracking-code": [{"type": 0, "value": "Código de rastreamento"}], "message.transfer-team-website-to-user": [{"type": 0, "value": "Transferir este site para sua conta?"}], "message.transfer-user-website-to-team": [{"type": 0, "value": "Selecione para qual equipe deseja transferir este site."}], "message.transfer-website": [{"type": 0, "value": "Transfira a propriedade do site para sua conta ou para outra equipe."}], "message.triggered-event": [{"type": 0, "value": "Evento disparado"}], "message.user-deleted": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> excluído."}], "message.viewed-page": [{"type": 0, "value": "Página visualizada"}], "message.visitor-log": [{"type": 0, "value": "Visitante de "}, {"type": 1, "value": "country"}, {"type": 0, "value": " usando o navegador "}, {"type": 1, "value": "browser"}, {"type": 0, "value": " em um "}, {"type": 1, "value": "device"}, {"type": 0, "value": " com sistema operacional "}, {"type": 1, "value": "os"}, {"type": 0, "value": "."}], "message.visitors-dropped-off": [{"type": 0, "value": "Visitantes abandonados"}]}