{"label.access-code": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.actions": [{"type": 0, "value": "Działania"}], "label.activity": [{"type": 0, "value": "Dziennik aktywności"}], "label.add": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.add-description": [{"type": 0, "value": "<PERSON><PERSON><PERSON> opis"}], "label.add-member": [{"type": 0, "value": "Dodaj <PERSON>łon<PERSON>"}], "label.add-step": [{"type": 0, "value": "<PERSON><PERSON>j krok"}], "label.add-website": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.admin": [{"type": 0, "value": "Administrator"}], "label.after": [{"type": 0, "value": "Po"}], "label.all": [{"type": 0, "value": "Wszystkie"}], "label.all-time": [{"type": 0, "value": "Cały czas"}], "label.analytics": [{"type": 0, "value": "Analityka"}], "label.average": [{"type": 0, "value": "Średnia"}], "label.back": [{"type": 0, "value": "Powró<PERSON>"}], "label.before": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.bounce-rate": [{"type": 0, "value": "Współczynnik odrzuceń"}], "label.breakdown": [{"type": 0, "value": "Rozbicie"}], "label.browser": [{"type": 0, "value": "Przeglądarka"}], "label.browsers": [{"type": 0, "value": "Przeglądarki"}], "label.cancel": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.change-password": [{"type": 0, "value": "<PERSON><PERSON><PERSON> hasło"}], "label.cities": [{"type": 0, "value": "Miasta"}], "label.city": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.clear-all": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wszystko"}], "label.compare": [{"type": 0, "value": "Porównaj"}], "label.confirm": [{"type": 0, "value": "Potwierdź"}], "label.confirm-password": [{"type": 0, "value": "Potwierd<PERSON> hasło"}], "label.contains": [{"type": 0, "value": "Zawiera"}], "label.continue": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.count": [{"type": 0, "value": "Liczba"}], "label.countries": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.country": [{"type": 0, "value": "Państwo"}], "label.create": [{"type": 0, "value": "Utwórz"}], "label.create-report": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON> raport"}], "label.create-team": [{"type": 0, "value": "Utwórz <PERSON>ł"}], "label.create-user": [{"type": 0, "value": "Utwórz użytkownika"}], "label.created": [{"type": 0, "value": "Utworzony"}], "label.created-by": [{"type": 0, "value": "Utworzony przez"}], "label.current": [{"type": 0, "value": "Aktualny"}], "label.current-password": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.custom-range": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.dashboard": [{"type": 0, "value": "Panel"}], "label.data": [{"type": 0, "value": "<PERSON>"}], "label.date": [{"type": 0, "value": "Data"}], "label.date-range": [{"type": 0, "value": "<PERSON><PERSON><PERSON> dat"}], "label.day": [{"type": 0, "value": "Dzień"}], "label.default-date-range": [{"type": 0, "value": "Domyślny zakres dat"}], "label.delete": [{"type": 0, "value": "Usuń"}], "label.delete-report": [{"type": 0, "value": "<PERSON><PERSON><PERSON> raport"}], "label.delete-team": [{"type": 0, "value": "Us<PERSON>ń zespół"}], "label.delete-user": [{"type": 0, "value": "Usuń użytkownika"}], "label.delete-website": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.description": [{"type": 0, "value": "Opis"}], "label.desktop": [{"type": 0, "value": "Komputer"}], "label.details": [{"type": 0, "value": "Szczegóły"}], "label.device": [{"type": 0, "value": "Urząd<PERSON><PERSON>"}], "label.devices": [{"type": 0, "value": "Urządzenia"}], "label.dismiss": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.does-not-contain": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.domain": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.dropoff": [{"type": 0, "value": "Odpływ"}], "label.edit": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.edit-dashboard": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> panel"}], "label.edit-member": [{"type": 0, "value": "Ed<PERSON><PERSON>j <PERSON>łon<PERSON>"}], "label.enable-share-url": [{"type": 0, "value": "Włącz udostępnianie adresu URL"}], "label.end-step": [{"type": 0, "value": "Krok końcowy"}], "label.entry": [{"type": 0, "value": "Entry URL"}], "label.event": [{"type": 0, "value": "Zdarzenie"}], "label.event-data": [{"type": 0, "value": "<PERSON>"}], "label.events": [{"type": 0, "value": "Zdarzenia"}], "label.exit": [{"type": 0, "value": "URL wyjściowy"}], "label.false": [{"type": 0, "value": "Fałsz"}], "label.field": [{"type": 0, "value": "Pole"}], "label.fields": [{"type": 0, "value": "Pola"}], "label.filter": [{"type": 0, "value": "Filtruj"}], "label.filter-combined": [{"type": 0, "value": "Połączone"}], "label.filter-raw": [{"type": 0, "value": "<PERSON><PERSON><PERSON> dane"}], "label.filters": [{"type": 0, "value": "Filtry"}], "label.first-seen": [{"type": 0, "value": "First seen"}], "label.funnel": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.funnel-description": [{"type": 0, "value": "Zrozum wskaźniki konwersji i odpływu użytkowników."}], "label.goal": [{"type": 0, "value": "<PERSON>l"}], "label.goals": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.goals-description": [{"type": 0, "value": "Track your goals for pageviews and events."}], "label.greater-than": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>ż"}], "label.greater-than-equals": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON> niż lub równe"}], "label.host": [{"type": 0, "value": "Host"}], "label.hosts": [{"type": 0, "value": "Hosty"}], "label.insights": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.insights-description": [{"type": 0, "value": "Poznaj lepiej swoje dane, korzystając z segmentów i filtrów."}], "label.is": [{"type": 0, "value": "Równe"}], "label.is-not": [{"type": 0, "value": "<PERSON>e jest równe"}], "label.is-not-set": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.is-set": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.join": [{"type": 0, "value": "Dołącz"}], "label.join-team": [{"type": 0, "value": "Dołącz do zespołu"}], "label.journey": [{"type": 0, "value": "Droga"}], "label.journey-description": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>, w jaki sposób użytkownicy poruszają się po Twojej wit<PERSON>."}], "label.language": [{"type": 0, "value": "Język"}], "label.languages": [{"type": 0, "value": "Języki"}], "label.laptop": [{"type": 0, "value": "Laptop"}], "label.last-days": [{"type": 0, "value": "Ostatnie "}, {"type": 1, "value": "x"}, {"type": 0, "value": " dni"}], "label.last-hours": [{"type": 0, "value": "Ostatnie "}, {"type": 1, "value": "x"}, {"type": 0, "value": " godzin"}], "label.last-months": [{"type": 0, "value": "<PERSON><PERSON><PERSON> "}, {"type": 1, "value": "x"}, {"type": 0, "value": " <PERSON><PERSON><PERSON><PERSON>"}], "label.last-seen": [{"type": 0, "value": "Last seen"}], "label.leave": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.leave-team": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> zespół"}], "label.less-than": [{"type": 0, "value": "Mniejsze niż"}], "label.less-than-equals": [{"type": 0, "value": "Mniejsze niż lub równe"}], "label.login": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.logout": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.manage": [{"type": 0, "value": "Manage"}], "label.manager": [{"type": 0, "value": "Manager"}], "label.max": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.member": [{"type": 0, "value": "Członek"}], "label.members": [{"type": 0, "value": "Członkowie"}], "label.min": [{"type": 0, "value": "Min"}], "label.mobile": [{"type": 0, "value": "Smartfon"}], "label.more": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>j"}], "label.my-account": [{"type": 0, "value": "<PERSON><PERSON> k<PERSON>"}], "label.my-websites": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.name": [{"type": 0, "value": "Nazwa"}], "label.new-password": [{"type": 0, "value": "Nowe hasło"}], "label.none": [{"type": 0, "value": "Brak"}], "label.number-of-records": [{"type": 1, "value": "x"}, {"type": 0, "value": " "}, {"offset": 0, "options": {"one": {"value": [{"type": 0, "value": "rekord"}]}, "other": {"value": [{"type": 0, "value": "rekordy"}]}}, "pluralType": "cardinal", "type": 6, "value": "x"}], "label.ok": [{"type": 0, "value": "OK"}], "label.os": [{"type": 0, "value": "OS"}], "label.overview": [{"type": 0, "value": "Przegląd"}], "label.owner": [{"type": 0, "value": "Właściciel"}], "label.page-of": [{"type": 0, "value": "Strona "}, {"type": 1, "value": "current"}, {"type": 0, "value": " z "}, {"type": 1, "value": "total"}], "label.page-views": [{"type": 0, "value": "Wyświ<PERSON><PERSON><PERSON> strony"}], "label.pageTitle": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.pages": [{"type": 0, "value": "Strony"}], "label.password": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.path": [{"type": 0, "value": "Path"}], "label.paths": [{"type": 0, "value": "Paths"}], "label.powered-by": [{"type": 0, "value": "Obsługiwane przez "}, {"type": 1, "value": "name"}], "label.previous": [{"type": 0, "value": "Poprzedni"}], "label.previous-period": [{"type": 0, "value": "Poprzedni okres"}], "label.previous-year": [{"type": 0, "value": "Poprzedni rok"}], "label.profile": [{"type": 0, "value": "Profil"}], "label.properties": [{"type": 0, "value": "Properties"}], "label.property": [{"type": 0, "value": "Property"}], "label.queries": [{"type": 0, "value": "Zapytania"}], "label.query": [{"type": 0, "value": "Zapytanie"}], "label.query-parameters": [{"type": 0, "value": "Parametry zapytania"}], "label.realtime": [{"type": 0, "value": "Czas rzeczywisty"}], "label.referrer": [{"type": 0, "value": "Źródło odsyłaj<PERSON>ce"}], "label.referrers": [{"type": 0, "value": "Źródła odsyłaj<PERSON>ce"}], "label.refresh": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.regenerate": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.region": [{"type": 0, "value": "Region"}], "label.regions": [{"type": 0, "value": "Regiony"}], "label.remove": [{"type": 0, "value": "Usuń"}], "label.remove-member": [{"type": 0, "value": "Usuń członka"}], "label.reports": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.required": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.reset": [{"type": 0, "value": "Zresetuj"}], "label.reset-website": [{"type": 0, "value": "Zresetuj statystyki"}], "label.retention": [{"type": 0, "value": "Re<PERSON><PERSON><PERSON>"}], "label.retention-description": [{"type": 0, "value": "Mierz przyciągającą siłę swojej strony internetowej, <PERSON><PERSON><PERSON><PERSON><PERSON>, jak czę<PERSON> użytkownicy powracają."}], "label.revenue": [{"type": 0, "value": "Revenue"}], "label.revenue-description": [{"type": 0, "value": "Look into your revenue across time."}], "label.revenue-property": [{"type": 0, "value": "Revenue Property"}], "label.role": [{"type": 0, "value": "Rola"}], "label.run-query": [{"type": 0, "value": "Uru<PERSON>m zap<PERSON>anie"}], "label.save": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.screens": [{"type": 0, "value": "E<PERSON><PERSON>"}], "label.search": [{"type": 0, "value": "Szukaj"}], "label.select": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.select-date": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.select-role": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> rolę"}], "label.select-website": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.session": [{"type": 0, "value": "Session"}], "label.sessions": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.settings": [{"type": 0, "value": "Ustawienia"}], "label.share-url": [{"type": 0, "value": "Udostępnij adres URL"}], "label.single-day": [{"type": 0, "value": "W tym dniu"}], "label.start-step": [{"type": 0, "value": "<PERSON>rok <PERSON>owy"}], "label.steps": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.sum": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.tablet": [{"type": 0, "value": "Tablet"}], "label.team": [{"type": 0, "value": "Zespół"}], "label.team-id": [{"type": 0, "value": "ID zespołu"}], "label.team-manager": [{"type": 0, "value": "Team manager"}], "label.team-member": [{"type": 0, "value": "Członek zespołu"}], "label.team-name": [{"type": 0, "value": "Nazwa zespołu"}], "label.team-owner": [{"type": 0, "value": "Właściciel zespołu"}], "label.team-view-only": [{"type": 0, "value": "Tylko do odczytu dla zespołu"}], "label.team-websites": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> zespołu"}], "label.teams": [{"type": 0, "value": "Zespoły"}], "label.theme": [{"type": 0, "value": "Motyw"}], "label.this-month": [{"type": 0, "value": "<PERSON> tym <PERSON>"}], "label.this-week": [{"type": 0, "value": "W tym tygodniu"}], "label.this-year": [{"type": 0, "value": "W tym roku"}], "label.timezone": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.title": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.today": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.toggle-charts": [{"type": 0, "value": "Przełącz wykresy"}], "label.total": [{"type": 0, "value": "W sumie"}], "label.total-records": [{"type": 0, "value": "Suma rekordów"}], "label.tracking-code": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.transactions": [{"type": 0, "value": "Transactions"}], "label.transfer": [{"type": 0, "value": "Transfer"}], "label.transfer-website": [{"type": 0, "value": "Transfer website"}], "label.true": [{"type": 0, "value": "Prawda"}], "label.type": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.unique": [{"type": 0, "value": "Unikalne"}], "label.unique-visitors": [{"type": 0, "value": "Unikalni odwiedzający"}], "label.uniqueCustomers": [{"type": 0, "value": "Unique Customers"}], "label.unknown": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.untitled": [{"type": 0, "value": "Bez tytułu"}], "label.update": [{"type": 0, "value": "Aktualizuj"}], "label.url": [{"type": 0, "value": "Link"}], "label.urls": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.user": [{"type": 0, "value": "Użytkownik"}], "label.user-property": [{"type": 0, "value": "User Property"}], "label.username": [{"type": 0, "value": "Nazwa użytkownika"}], "label.users": [{"type": 0, "value": "Użytkownicy"}], "label.utm": [{"type": 0, "value": "UTM"}], "label.utm-description": [{"type": 0, "value": "Śledź swoje kampanie za pomocą parametrów UTM."}], "label.value": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.view": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.view-details": [{"type": 0, "value": "Pokaż szczegóły"}], "label.view-only": [{"type": 0, "value": "Tylko do odczytu"}], "label.views": [{"type": 0, "value": "Wyświetlenia"}], "label.views-per-visit": [{"type": 0, "value": "Widoków na wizytę"}], "label.visit-duration": [{"type": 0, "value": "Średni czas wizyty"}], "label.visitors": [{"type": 0, "value": "Odwied<PERSON>j<PERSON><PERSON>"}], "label.visits": [{"type": 0, "value": "Wizyty"}], "label.website": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.website-id": [{"type": 0, "value": "ID witryny"}], "label.websites": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.window": [{"type": 0, "value": "Okno"}], "label.yesterday": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "message.action-confirmation": [{"type": 0, "value": "Wpisz "}, {"type": 1, "value": "confirmation"}, {"type": 0, "value": ", aby <PERSON>."}], "message.active-users": [{"type": 1, "value": "x"}, {"type": 0, "value": " aktualnie "}, {"offset": 0, "options": {"one": {"value": [{"type": 0, "value": "odwied<PERSON><PERSON><PERSON><PERSON>"}]}, "other": {"value": [{"type": 0, "value": "od<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}}, "pluralType": "cardinal", "type": 6, "value": "x"}], "message.collected-data": [{"type": 0, "value": "<PERSON><PERSON><PERSON> dane"}], "message.confirm-delete": [{"type": 0, "value": "Czy na pewno chcesz usunąć "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.confirm-leave": [{"type": 0, "value": "Czy na pewno chcesz opuścić "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.confirm-remove": [{"type": 0, "value": "Czy na pewno chcesz usunąć "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.confirm-reset": [{"type": 0, "value": "Czy na pewno chcesz zresetować statystyki "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.delete-team-warning": [{"type": 0, "value": "Usunięcie zespołu usunie wszystkie jego witryny."}], "message.delete-website-warning": [{"type": 0, "value": "Wszystkie powiązane dane również zostaną usunięte."}], "message.error": [{"type": 0, "value": "Coś poszło nie tak."}], "message.event-log": [{"type": 1, "value": "event"}, {"type": 0, "value": " na "}, {"type": 1, "value": "url"}], "message.go-to-settings": [{"type": 0, "value": "Przejdź do ustawień"}], "message.incorrect-username-password": [{"type": 0, "value": "Nieprawidłowa nazwa użytkownika lub hasło."}], "message.invalid-domain": [{"type": 0, "value": "Nieprawidłowa witryna"}], "message.min-password-length": [{"type": 0, "value": "Minimalna <PERSON> "}, {"type": 1, "value": "n"}, {"type": 0, "value": " znaków"}], "message.new-version-available": [{"type": 0, "value": "Now<PERSON> wersja Superlytics "}, {"type": 1, "value": "version"}, {"type": 0, "value": " jest dost<PERSON><PERSON><PERSON>!"}], "message.no-data-available": [{"type": 0, "value": "<PERSON><PERSON> danych."}], "message.no-event-data": [{"type": 0, "value": "Brak dostępnych danych o zdarzeniach."}], "message.no-match-password": [{"type": 0, "value": "Hasła się nie zgadzają"}], "message.no-results-found": [{"type": 0, "value": "Nie znaleziono wyników."}], "message.no-team-websites": [{"type": 0, "value": "Ten zespół nie ma żadnych witryn internetowych."}], "message.no-teams": [{"type": 0, "value": "Nie stworzyłeś żadnych zespołów."}], "message.no-users": [{"type": 0, "value": "Nie ma żadnych użytkowników."}], "message.no-websites-configured": [{"type": 0, "value": "<PERSON>e masz skonfigurowanych żadnych witryn internetowych."}], "message.page-not-found": [{"type": 0, "value": "Strona nie znaleziona."}], "message.reset-website": [{"type": 0, "value": "<PERSON><PERSON> <PERSON><PERSON><PERSON> tę wit<PERSON>, wpisz "}, {"type": 1, "value": "confirmation"}, {"type": 0, "value": " w polu poniżej, aby <PERSON>."}], "message.reset-website-warning": [{"type": 0, "value": "Wszystkie statystyki tej witryny zostaną usunięte, ale kod śledzenia pozostanie nienaruszony."}], "message.saved": [{"type": 0, "value": "Zapisano pomyślnie."}], "message.share-url": [{"type": 0, "value": "To jest publicznie udostępniany adres URL dla "}, {"type": 1, "value": "target"}, {"type": 0, "value": "."}], "message.team-already-member": [{"type": 0, "value": "Jesteś już członkiem zespołu."}], "message.team-not-found": [{"type": 0, "value": "Nie znaleziono zespołu."}], "message.team-websites-info": [{"type": 0, "value": "Strony internetowe mogą być przeglądane przez każdego członka zespołu."}], "message.tracking-code": [{"type": 0, "value": "<PERSON><PERSON>"}], "message.transfer-team-website-to-user": [{"type": 0, "value": "<PERSON><PERSON> przeni<PERSON> tę witrynę do Twoje konta?"}], "message.transfer-user-website-to-team": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>, do którego chcesz przenieść tę witrynę."}], "message.transfer-website": [{"type": 0, "value": "Przenieś własność witryny na swoje konto lub do innego zespołu."}], "message.triggered-event": [{"type": 0, "value": "Zdarzenie wyzwalające"}], "message.user-deleted": [{"type": 0, "value": "Użytkownik usunięty."}], "message.viewed-page": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>a"}], "message.visitor-log": [{"type": 0, "value": "Odwiedzający z "}, {"type": 1, "value": "country"}, {"type": 0, "value": " używa "}, {"type": 1, "value": "browser"}, {"type": 0, "value": " na "}, {"type": 1, "value": "os"}, {"type": 0, "value": " "}, {"type": 1, "value": "device"}], "message.visitors-dropped-off": [{"type": 0, "value": "Odpływ użytkowników"}]}