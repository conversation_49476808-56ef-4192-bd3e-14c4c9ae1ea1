{"label.access-code": [{"type": 0, "value": "Access code"}], "label.actions": [{"type": 0, "value": "Actions"}], "label.activity": [{"type": 0, "value": "Activity log"}], "label.add": [{"type": 0, "value": "Add"}], "label.add-description": [{"type": 0, "value": "Add description"}], "label.add-member": [{"type": 0, "value": "Add member"}], "label.add-step": [{"type": 0, "value": "Add step"}], "label.add-website": [{"type": 0, "value": "Add website"}], "label.admin": [{"type": 0, "value": "Administrator"}], "label.after": [{"type": 0, "value": "After"}], "label.all": [{"type": 0, "value": "All"}], "label.all-time": [{"type": 0, "value": "All time"}], "label.analytics": [{"type": 0, "value": "Analytics"}], "label.average": [{"type": 0, "value": "Average"}], "label.back": [{"type": 0, "value": "Back"}], "label.before": [{"type": 0, "value": "Before"}], "label.bounce-rate": [{"type": 0, "value": "Bounce rate"}], "label.breakdown": [{"type": 0, "value": "Breakdown"}], "label.browser": [{"type": 0, "value": "Browser"}], "label.browsers": [{"type": 0, "value": "Browsers"}], "label.cancel": [{"type": 0, "value": "Cancel"}], "label.change-password": [{"type": 0, "value": "Change password"}], "label.cities": [{"type": 0, "value": "Cities"}], "label.city": [{"type": 0, "value": "City"}], "label.clear-all": [{"type": 0, "value": "Clear all"}], "label.compare": [{"type": 0, "value": "Compare"}], "label.confirm": [{"type": 0, "value": "Confirm"}], "label.confirm-password": [{"type": 0, "value": "Confirm password"}], "label.contains": [{"type": 0, "value": "Contains"}], "label.continue": [{"type": 0, "value": "Continue"}], "label.count": [{"type": 0, "value": "Count"}], "label.countries": [{"type": 0, "value": "Countries"}], "label.country": [{"type": 0, "value": "Country"}], "label.create": [{"type": 0, "value": "Create"}], "label.create-report": [{"type": 0, "value": "Create report"}], "label.create-team": [{"type": 0, "value": "Create team"}], "label.create-user": [{"type": 0, "value": "Create user"}], "label.created": [{"type": 0, "value": "Created"}], "label.created-by": [{"type": 0, "value": "Created By"}], "label.current": [{"type": 0, "value": "Current"}], "label.current-password": [{"type": 0, "value": "Current password"}], "label.custom-range": [{"type": 0, "value": "Custom range"}], "label.dashboard": [{"type": 0, "value": "Dashboard"}], "label.data": [{"type": 0, "value": "Data"}], "label.date": [{"type": 0, "value": "Date"}], "label.date-range": [{"type": 0, "value": "Date range"}], "label.day": [{"type": 0, "value": "Day"}], "label.default-date-range": [{"type": 0, "value": "Default date range"}], "label.delete": [{"type": 0, "value": "Delete"}], "label.delete-report": [{"type": 0, "value": "Delete report"}], "label.delete-team": [{"type": 0, "value": "Delete team"}], "label.delete-user": [{"type": 0, "value": "Delete user"}], "label.delete-website": [{"type": 0, "value": "Delete website"}], "label.description": [{"type": 0, "value": "Description"}], "label.desktop": [{"type": 0, "value": "Desktop"}], "label.details": [{"type": 0, "value": "Details"}], "label.device": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.devices": [{"type": 0, "value": "Devices"}], "label.dismiss": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.does-not-contain": [{"type": 0, "value": "Does not contain"}], "label.domain": [{"type": 0, "value": "Domain"}], "label.dropoff": [{"type": 0, "value": "Dropoff"}], "label.edit": [{"type": 0, "value": "Edit"}], "label.edit-dashboard": [{"type": 0, "value": "Edit dashboard"}], "label.edit-member": [{"type": 0, "value": "Edit member"}], "label.enable-share-url": [{"type": 0, "value": "Enable share URL"}], "label.end-step": [{"type": 0, "value": "End Step"}], "label.entry": [{"type": 0, "value": "Entry URL"}], "label.event": [{"type": 0, "value": "Event"}], "label.event-data": [{"type": 0, "value": "Event Data"}], "label.events": [{"type": 0, "value": "Events"}], "label.exit": [{"type": 0, "value": "Exit URL"}], "label.false": [{"type": 0, "value": "False"}], "label.field": [{"type": 0, "value": "Field"}], "label.fields": [{"type": 0, "value": "Fields"}], "label.filter": [{"type": 0, "value": "Filter"}], "label.filter-combined": [{"type": 0, "value": "Combined"}], "label.filter-raw": [{"type": 0, "value": "Raw"}], "label.filters": [{"type": 0, "value": "Filters"}], "label.first-seen": [{"type": 0, "value": "First seen"}], "label.funnel": [{"type": 0, "value": "Funnel"}], "label.funnel-description": [{"type": 0, "value": "Understand the conversion and drop-off rate of users."}], "label.goal": [{"type": 0, "value": "Goal"}], "label.goals": [{"type": 0, "value": "Goals"}], "label.goals-description": [{"type": 0, "value": "Track your goals for pageviews and events."}], "label.greater-than": [{"type": 0, "value": "Greater than"}], "label.greater-than-equals": [{"type": 0, "value": "Greater than or equals"}], "label.host": [{"type": 0, "value": "Host"}], "label.hosts": [{"type": 0, "value": "Hosts"}], "label.insights": [{"type": 0, "value": "Insights"}], "label.insights-description": [{"type": 0, "value": "Dive deeper into your data by using segments and filters."}], "label.is": [{"type": 0, "value": "Is"}], "label.is-not": [{"type": 0, "value": "Is not"}], "label.is-not-set": [{"type": 0, "value": "Is not set"}], "label.is-set": [{"type": 0, "value": "Is set"}], "label.join": [{"type": 0, "value": "Join"}], "label.join-team": [{"type": 0, "value": "Join team"}], "label.journey": [{"type": 0, "value": "Journey"}], "label.journey-description": [{"type": 0, "value": "Understand how users navigate through your website."}], "label.language": [{"type": 0, "value": "Language"}], "label.languages": [{"type": 0, "value": "Languages"}], "label.laptop": [{"type": 0, "value": "Laptop"}], "label.last-days": [{"type": 0, "value": "Last "}, {"type": 1, "value": "x"}, {"type": 0, "value": " days"}], "label.last-hours": [{"type": 0, "value": "Last "}, {"type": 1, "value": "x"}, {"type": 0, "value": " hours"}], "label.last-months": [{"type": 0, "value": "Last "}, {"type": 1, "value": "x"}, {"type": 0, "value": " months"}], "label.last-seen": [{"type": 0, "value": "Last seen"}], "label.leave": [{"type": 0, "value": "Leave"}], "label.leave-team": [{"type": 0, "value": "Leave team"}], "label.less-than": [{"type": 0, "value": "Less than"}], "label.less-than-equals": [{"type": 0, "value": "Less than or equals"}], "label.login": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.logout": [{"type": 0, "value": "Logout"}], "label.manage": [{"type": 0, "value": "Manage"}], "label.manager": [{"type": 0, "value": "Manager"}], "label.max": [{"type": 0, "value": "Max"}], "label.member": [{"type": 0, "value": "Member"}], "label.members": [{"type": 0, "value": "Members"}], "label.min": [{"type": 0, "value": "Min"}], "label.mobile": [{"type": 0, "value": "Mobile"}], "label.more": [{"type": 0, "value": "More"}], "label.my-account": [{"type": 0, "value": "My account"}], "label.my-websites": [{"type": 0, "value": "My websites"}], "label.name": [{"type": 0, "value": "Name"}], "label.new-password": [{"type": 0, "value": "New password"}], "label.none": [{"type": 0, "value": "None"}], "label.number-of-records": [{"type": 1, "value": "x"}, {"type": 0, "value": " "}, {"offset": 0, "options": {"one": {"value": [{"type": 0, "value": "record"}]}, "other": {"value": [{"type": 0, "value": "records"}]}}, "pluralType": "cardinal", "type": 6, "value": "x"}], "label.ok": [{"type": 0, "value": "OK"}], "label.os": [{"type": 0, "value": "OS"}], "label.overview": [{"type": 0, "value": "Overview"}], "label.owner": [{"type": 0, "value": "Owner"}], "label.page-of": [{"type": 0, "value": "Page "}, {"type": 1, "value": "current"}, {"type": 0, "value": " of "}, {"type": 1, "value": "total"}], "label.page-views": [{"type": 0, "value": "Page views"}], "label.pageTitle": [{"type": 0, "value": "Page title"}], "label.pages": [{"type": 0, "value": "Pages"}], "label.password": [{"type": 0, "value": "Password"}], "label.path": [{"type": 0, "value": "Path"}], "label.paths": [{"type": 0, "value": "Paths"}], "label.powered-by": [{"type": 0, "value": "Powered by "}, {"type": 1, "value": "name"}], "label.previous": [{"type": 0, "value": "Previous"}], "label.previous-period": [{"type": 0, "value": "Previous period"}], "label.previous-year": [{"type": 0, "value": "Previous year"}], "label.profile": [{"type": 0, "value": "Profile"}], "label.properties": [{"type": 0, "value": "Properties"}], "label.property": [{"type": 0, "value": "Property"}], "label.queries": [{"type": 0, "value": "Queries"}], "label.query": [{"type": 0, "value": "Query"}], "label.query-parameters": [{"type": 0, "value": "Query parameters"}], "label.realtime": [{"type": 0, "value": "Realtime"}], "label.referrer": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.referrers": [{"type": 0, "value": "Referrers"}], "label.refresh": [{"type": 0, "value": "Refresh"}], "label.regenerate": [{"type": 0, "value": "Regenerate"}], "label.region": [{"type": 0, "value": "Region"}], "label.regions": [{"type": 0, "value": "Regions"}], "label.remove": [{"type": 0, "value": "Remove"}], "label.remove-member": [{"type": 0, "value": "Remove member"}], "label.reports": [{"type": 0, "value": "Reports"}], "label.required": [{"type": 0, "value": "Required"}], "label.reset": [{"type": 0, "value": "Reset"}], "label.reset-website": [{"type": 0, "value": "Reset statistics"}], "label.retention": [{"type": 0, "value": "Retention"}], "label.retention-description": [{"type": 0, "value": "Measure your website stickiness by tracking how often users return."}], "label.revenue": [{"type": 0, "value": "Revenue"}], "label.revenue-description": [{"type": 0, "value": "Look into your revenue across time."}], "label.revenue-property": [{"type": 0, "value": "Revenue Property"}], "label.role": [{"type": 0, "value": "Role"}], "label.run-query": [{"type": 0, "value": "Run query"}], "label.save": [{"type": 0, "value": "Save"}], "label.screens": [{"type": 0, "value": "Screens"}], "label.search": [{"type": 0, "value": "Search"}], "label.select": [{"type": 0, "value": "Select"}], "label.select-date": [{"type": 0, "value": "Select date"}], "label.select-role": [{"type": 0, "value": "Select role"}], "label.select-website": [{"type": 0, "value": "Select website"}], "label.session": [{"type": 0, "value": "Session"}], "label.sessions": [{"type": 0, "value": "Sessions"}], "label.settings": [{"type": 0, "value": "Settings"}], "label.share-url": [{"type": 0, "value": "Share URL"}], "label.single-day": [{"type": 0, "value": "Single day"}], "label.start-step": [{"type": 0, "value": "Start Step"}], "label.steps": [{"type": 0, "value": "Steps"}], "label.sum": [{"type": 0, "value": "Sum"}], "label.tablet": [{"type": 0, "value": "Tablet"}], "label.team": [{"type": 0, "value": "Team"}], "label.team-id": [{"type": 0, "value": "Team ID"}], "label.team-manager": [{"type": 0, "value": "Team manager"}], "label.team-member": [{"type": 0, "value": "Team member"}], "label.team-name": [{"type": 0, "value": "Team name"}], "label.team-owner": [{"type": 0, "value": "Team owner"}], "label.team-view-only": [{"type": 0, "value": "Team view only"}], "label.team-websites": [{"type": 0, "value": "Team websites"}], "label.teams": [{"type": 0, "value": "Teams"}], "label.theme": [{"type": 0, "value": "Theme"}], "label.this-month": [{"type": 0, "value": "This month"}], "label.this-week": [{"type": 0, "value": "This week"}], "label.this-year": [{"type": 0, "value": "This year"}], "label.timezone": [{"type": 0, "value": "Timezone"}], "label.title": [{"type": 0, "value": "Title"}], "label.today": [{"type": 0, "value": "Today"}], "label.toggle-charts": [{"type": 0, "value": "Toggle charts"}], "label.total": [{"type": 0, "value": "Total"}], "label.total-records": [{"type": 0, "value": "Total records"}], "label.tracking-code": [{"type": 0, "value": "Tracking code"}], "label.transactions": [{"type": 0, "value": "Transactions"}], "label.transfer": [{"type": 0, "value": "Transfer"}], "label.transfer-website": [{"type": 0, "value": "Transfer website"}], "label.true": [{"type": 0, "value": "True"}], "label.type": [{"type": 0, "value": "Type"}], "label.unique": [{"type": 0, "value": "Unique"}], "label.unique-visitors": [{"type": 0, "value": "Unique visitors"}], "label.uniqueCustomers": [{"type": 0, "value": "Unique Customers"}], "label.unknown": [{"type": 0, "value": "Unknown"}], "label.untitled": [{"type": 0, "value": "Untitled"}], "label.update": [{"type": 0, "value": "Update"}], "label.url": [{"type": 0, "value": "URL"}], "label.urls": [{"type": 0, "value": "URLs"}], "label.user": [{"type": 0, "value": "User"}], "label.user-property": [{"type": 0, "value": "User Property"}], "label.username": [{"type": 0, "value": "Username"}], "label.users": [{"type": 0, "value": "Users"}], "label.utm": [{"type": 0, "value": "UTM"}], "label.utm-description": [{"type": 0, "value": "Track your campaigns through UTM parameters."}], "label.value": [{"type": 0, "value": "Value"}], "label.view": [{"type": 0, "value": "View"}], "label.view-details": [{"type": 0, "value": "View details"}], "label.view-only": [{"type": 0, "value": "View only"}], "label.views": [{"type": 0, "value": "Views"}], "label.views-per-visit": [{"type": 0, "value": "Views per visit"}], "label.visit-duration": [{"type": 0, "value": "Visit duration"}], "label.visitors": [{"type": 0, "value": "Visitors"}], "label.visits": [{"type": 0, "value": "Visits"}], "label.website": [{"type": 0, "value": "Website"}], "label.website-id": [{"type": 0, "value": "Website ID"}], "label.websites": [{"type": 0, "value": "Websites"}], "label.window": [{"type": 0, "value": "Window"}], "label.yesterday": [{"type": 0, "value": "Yesterday"}], "message.action-confirmation": [{"type": 0, "value": "Type "}, {"type": 1, "value": "confirmation"}, {"type": 0, "value": " in the box below to confirm."}], "message.active-users": [{"type": 1, "value": "x"}, {"type": 0, "value": " current "}, {"offset": 0, "options": {"one": {"value": [{"type": 0, "value": "visitor"}]}, "other": {"value": [{"type": 0, "value": "visitors"}]}}, "pluralType": "cardinal", "type": 6, "value": "x"}], "message.collected-data": [{"type": 0, "value": "Collected data"}], "message.confirm-delete": [{"type": 0, "value": "Are you sure you want to delete "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.confirm-leave": [{"type": 0, "value": "Are you sure you want to leave "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.confirm-remove": [{"type": 0, "value": "Are you sure you want to remove "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.confirm-reset": [{"type": 0, "value": "Are you sure you want to reset "}, {"type": 1, "value": "target"}, {"type": 0, "value": "'s statistics?"}], "message.delete-team-warning": [{"type": 0, "value": "Deleting a team will also delete all team websites."}], "message.delete-website-warning": [{"type": 0, "value": "All website data will be deleted."}], "message.error": [{"type": 0, "value": "Something went wrong."}], "message.event-log": [{"type": 1, "value": "event"}, {"type": 0, "value": " on "}, {"type": 1, "value": "url"}], "message.go-to-settings": [{"type": 0, "value": "Go to settings"}], "message.incorrect-username-password": [{"type": 0, "value": "Incorrect username/password."}], "message.invalid-domain": [{"type": 0, "value": "Invalid domain. Do not include http/https."}], "message.min-password-length": [{"type": 0, "value": "Minimum length of "}, {"type": 1, "value": "n"}, {"type": 0, "value": " characters"}], "message.new-version-available": [{"type": 0, "value": "A new version of Superlytics "}, {"type": 1, "value": "version"}, {"type": 0, "value": " is available!"}], "message.no-data-available": [{"type": 0, "value": "No data available."}], "message.no-event-data": [{"type": 0, "value": "No event data is available."}], "message.no-match-password": [{"type": 0, "value": "Passwords do not match."}], "message.no-results-found": [{"type": 0, "value": "No results were found."}], "message.no-team-websites": [{"type": 0, "value": "This team does not have any websites."}], "message.no-teams": [{"type": 0, "value": "You have not created any teams."}], "message.no-users": [{"type": 0, "value": "There are no users."}], "message.no-websites-configured": [{"type": 0, "value": "You do not have any websites configured."}], "message.page-not-found": [{"type": 0, "value": "Page not found."}], "message.reset-website": [{"type": 0, "value": "To reset this website, type "}, {"type": 1, "value": "confirmation"}, {"type": 0, "value": " in the box below to confirm."}], "message.reset-website-warning": [{"type": 0, "value": "All statistics for this website will be deleted, but your settings will remain intact."}], "message.saved": [{"type": 0, "value": "Saved."}], "message.share-url": [{"type": 0, "value": "This is the publicly shared URL for "}, {"type": 1, "value": "target"}, {"type": 0, "value": "."}], "message.team-already-member": [{"type": 0, "value": "You are already a member of the team."}], "message.team-not-found": [{"type": 0, "value": "Team not found."}], "message.team-websites-info": [{"type": 0, "value": "Websites can be viewed by anyone on the team."}], "message.tracking-code": [{"type": 0, "value": "To track stats for this website, place the following code in the "}, {"children": [{"type": 0, "value": "..."}], "type": 8, "value": "head"}, {"type": 0, "value": " section of your HTML."}], "message.transfer-team-website-to-user": [{"type": 0, "value": "Transfer this website to your account?"}], "message.transfer-user-website-to-team": [{"type": 0, "value": "Select the team to transfer this website to."}], "message.transfer-website": [{"type": 0, "value": "Transfer website ownership to your account or another team."}], "message.triggered-event": [{"type": 0, "value": "Triggered event"}], "message.user-deleted": [{"type": 0, "value": "User deleted."}], "message.viewed-page": [{"type": 0, "value": "Viewed page"}], "message.visitor-log": [{"type": 0, "value": "Visitor from "}, {"type": 1, "value": "country"}, {"type": 0, "value": " using "}, {"type": 1, "value": "browser"}, {"type": 0, "value": " on "}, {"type": 1, "value": "os"}, {"type": 0, "value": " "}, {"type": 1, "value": "device"}], "message.visitors-dropped-off": [{"type": 0, "value": "Visitors dropped off"}]}