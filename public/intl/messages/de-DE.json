{"label.access-code": [{"type": 0, "value": "Zugangscode"}], "label.actions": [{"type": 0, "value": "Aktionen"}], "label.activity": [{"type": 0, "value": "Aktivitätsverlauf"}], "label.add": [{"type": 0, "value": "Hinzufügen"}], "label.add-description": [{"type": 0, "value": "Beschreibung hinzufügen"}], "label.add-member": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> hinz<PERSON>"}], "label.add-step": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.add-website": [{"type": 0, "value": "Website hinzufügen"}], "label.admin": [{"type": 0, "value": "Administrator"}], "label.after": [{"type": 0, "value": "Nach"}], "label.all": [{"type": 0, "value": "Alle"}], "label.all-time": [{"type": 0, "value": "Gesamter Zeit<PERSON>um"}], "label.analytics": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.average": [{"type": 0, "value": "Durchschnitt"}], "label.back": [{"type": 0, "value": "Zurück"}], "label.before": [{"type": 0, "value": "Vor"}], "label.bounce-rate": [{"type": 0, "value": "Absprungrate"}], "label.breakdown": [{"type": 0, "value": "Aufschlüsselung"}], "label.browser": [{"type": 0, "value": "Browser"}], "label.browsers": [{"type": 0, "value": "Browser"}], "label.cancel": [{"type": 0, "value": "Abbrechen"}], "label.change-password": [{"type": 0, "value": "Passwort ändern"}], "label.cities": [{"type": 0, "value": "Städte"}], "label.city": [{"type": 0, "value": "Stadt"}], "label.clear-all": [{"type": 0, "value": "Alles löschen"}], "label.compare": [{"type": 0, "value": "Vergleichen"}], "label.confirm": [{"type": 0, "value": "Bestätigen"}], "label.confirm-password": [{"type": 0, "value": "Passwort wiederholen"}], "label.contains": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.continue": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.count": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.countries": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.country": [{"type": 0, "value": "Land"}], "label.create": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.create-report": [{"type": 0, "value": "Bericht erstellen"}], "label.create-team": [{"type": 0, "value": "Team erstellen"}], "label.create-user": [{"type": 0, "value": "<PERSON><PERSON><PERSON> er<PERSON>"}], "label.created": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.created-by": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> von"}], "label.current": [{"type": 0, "value": "Aktuell"}], "label.current-password": [{"type": 0, "value": "Derzeitiges Passwort"}], "label.custom-range": [{"type": 0, "value": "Benutzerdefinierter Bereich"}], "label.dashboard": [{"type": 0, "value": "Übersicht"}], "label.data": [{"type": 0, "value": "Daten"}], "label.date": [{"type": 0, "value": "Datum"}], "label.date-range": [{"type": 0, "value": "Datumsbereich"}], "label.day": [{"type": 0, "value": "Tag"}], "label.default-date-range": [{"type": 0, "value": "Voreingestellter Datumsbereich"}], "label.delete": [{"type": 0, "value": "Löschen"}], "label.delete-report": [{"type": 0, "value": "Bericht löschen"}], "label.delete-team": [{"type": 0, "value": "Team löschen"}], "label.delete-user": [{"type": 0, "value": "Benutzer löschen"}], "label.delete-website": [{"type": 0, "value": "Website löschen"}], "label.description": [{"type": 0, "value": "Beschreibung"}], "label.desktop": [{"type": 0, "value": "Desktop"}], "label.details": [{"type": 0, "value": "Details"}], "label.device": [{"type": 0, "value": "G<PERSON><PERSON>"}], "label.devices": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.dismiss": [{"type": 0, "value": "Verwerfen"}], "label.does-not-contain": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> nicht"}], "label.domain": [{"type": 0, "value": "Domain"}], "label.dropoff": [{"type": 0, "value": "Absprung"}], "label.edit": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.edit-dashboard": [{"type": 0, "value": "Dashboard bearbeiten"}], "label.edit-member": [{"type": 0, "value": "Mit<PERSON><PERSON> bearbeiten"}], "label.enable-share-url": [{"type": 0, "value": "Freigabe-URL aktivieren"}], "label.end-step": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.entry": [{"type": 0, "value": "Eingangs-URL"}], "label.event": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.event-data": [{"type": 0, "value": "Ereignisdaten"}], "label.events": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.exit": [{"type": 0, "value": "Ausgangs-URL"}], "label.false": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.field": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.fields": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.filter": [{"type": 0, "value": "Filter"}], "label.filter-combined": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.filter-raw": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.filters": [{"type": 0, "value": "Filter"}], "label.first-seen": [{"type": 0, "value": "Erstmalig g<PERSON>hen"}], "label.funnel": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.funnel-description": [{"type": 0, "value": "Verstehen Sie die Konversions- und Absprungrate Ihrer Nutzer."}], "label.goal": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.goals": [{"type": 0, "value": "Ziele"}], "label.goals-description": [{"type": 0, "value": "Verfolgen Sie Ihre Ziele für Seitenaufrufe und Ereignisse."}], "label.greater-than": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON> als"}], "label.greater-than-equals": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON> oder gleich"}], "label.host": [{"type": 0, "value": "Host"}], "label.hosts": [{"type": 0, "value": "Hosts"}], "label.insights": [{"type": 0, "value": "Einblick<PERSON>"}], "label.insights-description": [{"type": 0, "value": "Vertiefen Sie sich mit Hilf<PERSON> von Segmenten und Filtern in Ihre Daten."}], "label.is": [{"type": 0, "value": "Ist"}], "label.is-not": [{"type": 0, "value": "<PERSON><PERSON> nicht"}], "label.is-not-set": [{"type": 0, "value": "Ist nicht gesetzt"}], "label.is-set": [{"type": 0, "value": "Ist gesetzt"}], "label.join": [{"type": 0, "value": "Beitreten"}], "label.join-team": [{"type": 0, "value": "Team beitreten"}], "label.journey": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.journey-description": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>, wie Nutzer auf Ihrer Website navigieren."}], "label.language": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.languages": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.laptop": [{"type": 0, "value": "Laptop"}], "label.last-days": [{"type": 0, "value": "Letzten "}, {"type": 1, "value": "x"}, {"type": 0, "value": " Tage"}], "label.last-hours": [{"type": 0, "value": "Letzten "}, {"type": 1, "value": "x"}, {"type": 0, "value": " Stunden"}], "label.last-months": [{"type": 0, "value": "Letzten "}, {"type": 1, "value": "x"}, {"type": 0, "value": " Monate"}], "label.last-seen": [{"type": 0, "value": "Zuletzt g<PERSON>hen"}], "label.leave": [{"type": 0, "value": "Verlassen"}], "label.leave-team": [{"type": 0, "value": "Team verlassen"}], "label.less-than": [{"type": 0, "value": "<PERSON><PERSON>s"}], "label.less-than-equals": [{"type": 0, "value": "<PERSON>er oder gleich"}], "label.login": [{"type": 0, "value": "Anmelden"}], "label.logout": [{"type": 0, "value": "Abmelden"}], "label.manage": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.manager": [{"type": 0, "value": "Verwaltung"}], "label.max": [{"type": 0, "value": "Max"}], "label.member": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.members": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.min": [{"type": 0, "value": "Min"}], "label.mobile": [{"type": 0, "value": "Handy"}], "label.more": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.my-account": [{"type": 0, "value": "<PERSON><PERSON> Account"}], "label.my-websites": [{"type": 0, "value": "Meine Websites"}], "label.name": [{"type": 0, "value": "Name"}], "label.new-password": [{"type": 0, "value": "Neues Passwort"}], "label.none": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.number-of-records": [{"type": 1, "value": "x"}, {"type": 0, "value": " "}, {"offset": 0, "options": {"one": {"value": [{"type": 0, "value": "record"}]}, "other": {"value": [{"type": 0, "value": "records"}]}}, "pluralType": "cardinal", "type": 6, "value": "x"}], "label.ok": [{"type": 0, "value": "OK"}], "label.os": [{"type": 0, "value": "OS"}], "label.overview": [{"type": 0, "value": "Übersicht"}], "label.owner": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.page-of": [{"type": 0, "value": "Seite "}, {"type": 1, "value": "current"}, {"type": 0, "value": " von "}, {"type": 1, "value": "total"}], "label.page-views": [{"type": 0, "value": "Seitenaufrufe"}], "label.pageTitle": [{"type": 0, "value": "Seitentitel"}], "label.pages": [{"type": 0, "value": "Seiten"}], "label.password": [{"type": 0, "value": "Passwort"}], "label.path": [{"type": 0, "value": "Pfad"}], "label.paths": [{"type": 0, "value": "Pfade"}], "label.powered-by": [{"type": 0, "value": "Betrieben durch "}, {"type": 1, "value": "name"}], "label.previous": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.previous-period": [{"type": 0, "value": "Vorherige Periode"}], "label.previous-year": [{"type": 0, "value": "Vorheriges Jahr"}], "label.profile": [{"type": 0, "value": "Profil"}], "label.properties": [{"type": 0, "value": "Eigenschaften"}], "label.property": [{"type": 0, "value": "Eigentum"}], "label.queries": [{"type": 0, "value": "Abfragen"}], "label.query": [{"type": 0, "value": "Abfrage"}], "label.query-parameters": [{"type": 0, "value": "Abfrageparameter"}], "label.realtime": [{"type": 0, "value": "Echtzeit"}], "label.referrer": [{"type": 0, "value": "Übermittler"}], "label.referrers": [{"type": 0, "value": "Übermittler"}], "label.refresh": [{"type": 0, "value": "Aktualisieren"}], "label.regenerate": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.region": [{"type": 0, "value": "Region"}], "label.regions": [{"type": 0, "value": "Regionen"}], "label.remove": [{"type": 0, "value": "Entfernen"}], "label.remove-member": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> entfernen"}], "label.reports": [{"type": 0, "value": "Berichte"}], "label.required": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.reset": [{"type": 0, "value": "Z<PERSON>ücksetzen"}], "label.reset-website": [{"type": 0, "value": "Statistik zurücksetzen"}], "label.retention": [{"type": 0, "value": "Erhalt"}], "label.retention-description": [{"type": 0, "value": "Messen Sie die Verweildauer auf Ihrer Website, indem Sie verfolgen, wie oft die Nutzer zurückkehren."}], "label.revenue": [{"type": 0, "value": "Umsatz"}], "label.revenue-description": [{"type": 0, "value": "<PERSON>ben Sie einen Blick auf Ihre Umsätze im Laufe der Zeit."}], "label.revenue-property": [{"type": 0, "value": "Umsatzeigenschaften"}], "label.role": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.run-query": [{"type": 0, "value": "Abfrage starten"}], "label.save": [{"type": 0, "value": "Speichern"}], "label.screens": [{"type": 0, "value": "Bildschirmauflösungen"}], "label.search": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.select": [{"type": 0, "value": "Auswählen"}], "label.select-date": [{"type": 0, "value": "Da<PERSON> ausw<PERSON>en"}], "label.select-role": [{"type": 0, "value": "Rolle auswählen"}], "label.select-website": [{"type": 0, "value": "Website auswählen"}], "label.session": [{"type": 0, "value": "Sit<PERSON>ng"}], "label.sessions": [{"type": 0, "value": "Sitzungen"}], "label.settings": [{"type": 0, "value": "Einstellungen"}], "label.share-url": [{"type": 0, "value": "Freigabe-URL"}], "label.single-day": [{"type": 0, "value": "Ein Tag"}], "label.start-step": [{"type": 0, "value": "Start<PERSON><PERSON>t"}], "label.steps": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.sum": [{"type": 0, "value": "Summe"}], "label.tablet": [{"type": 0, "value": "Tablet"}], "label.team": [{"type": 0, "value": "Team"}], "label.team-id": [{"type": 0, "value": "Team-ID"}], "label.team-manager": [{"type": 0, "value": "Team-Manager"}], "label.team-member": [{"type": 0, "value": "Team-<PERSON><PERSON><PERSON><PERSON>"}], "label.team-name": [{"type": 0, "value": "Name des Teams"}], "label.team-owner": [{"type": 0, "value": "Team-<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.team-view-only": [{"type": 0, "value": "Nur für Team-Mitglieder sichtbar"}], "label.team-websites": [{"type": 0, "value": "Team-Websites"}], "label.teams": [{"type": 0, "value": "Teams"}], "label.theme": [{"type": 0, "value": "<PERSON>a"}], "label.this-month": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.this-week": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.this-year": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.timezone": [{"type": 0, "value": "Zeitzone"}], "label.title": [{"type": 0, "value": "Titel"}], "label.today": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.toggle-charts": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> umschal<PERSON>"}], "label.total": [{"type": 0, "value": "Gesamt"}], "label.total-records": [{"type": 0, "value": "Datensätze insgesamt"}], "label.tracking-code": [{"type": 0, "value": "Tracking Code"}], "label.transactions": [{"type": 0, "value": "Transaktionen"}], "label.transfer": [{"type": 0, "value": "Übertragung"}], "label.transfer-website": [{"type": 0, "value": "Website übertragen"}], "label.true": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.type": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.unique": [{"type": 0, "value": "Einzigartig"}], "label.unique-visitors": [{"type": 0, "value": "Einzigartige Besucher"}], "label.uniqueCustomers": [{"type": 0, "value": "Einzigartige Kunden"}], "label.unknown": [{"type": 0, "value": "Unbekannt"}], "label.untitled": [{"type": 0, "value": "Unbenannt"}], "label.update": [{"type": 0, "value": "Update"}], "label.url": [{"type": 0, "value": "URL"}], "label.urls": [{"type": 0, "value": "URLs"}], "label.user": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.user-property": [{"type": 0, "value": "Benutzereigenschaften"}], "label.username": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.users": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.utm": [{"type": 0, "value": "UTM"}], "label.utm-description": [{"type": 0, "value": "Tracken Sie Ihre Kampagnen mit UTM Parametern."}], "label.value": [{"type": 0, "value": "Wert"}], "label.view": [{"type": 0, "value": "Anzeigen"}], "label.view-details": [{"type": 0, "value": "Details anzeigen"}], "label.view-only": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.views": [{"type": 0, "value": "Auf<PERSON><PERSON>"}], "label.views-per-visit": [{"type": 0, "value": "Aufrufe pro Besuch"}], "label.visit-duration": [{"type": 0, "value": "Durchschn. Besuchszeit"}], "label.visitors": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.visits": [{"type": 0, "value": "Besuche"}], "label.website": [{"type": 0, "value": "Website"}], "label.website-id": [{"type": 0, "value": "Website-ID"}], "label.websites": [{"type": 0, "value": "Websites"}], "label.window": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.yesterday": [{"type": 0, "value": "Gestern"}], "message.action-confirmation": [{"type": 0, "value": "Schreibe "}, {"type": 1, "value": "confirmation"}, {"type": 0, "value": " in die Box zur bestätigung."}], "message.active-users": [{"type": 1, "value": "x"}, {"type": 0, "value": " "}, {"offset": 0, "options": {"one": {"value": [{"type": 0, "value": "aktiver Besucher"}]}, "other": {"value": [{"type": 0, "value": "aktive Besucher"}]}}, "pluralType": "cardinal", "type": 6, "value": "x"}], "message.collected-data": [{"type": 0, "value": "Gesammelte Daten"}], "message.confirm-delete": [{"type": 0, "value": "Sind Sie sich sicher, "}, {"type": 1, "value": "target"}, {"type": 0, "value": " zu löschen?"}], "message.confirm-leave": [{"type": 0, "value": "Sind <PERSON> sicher, dass die "}, {"type": 1, "value": "target"}, {"type": 0, "value": " verlassen möchten?"}], "message.confirm-remove": [{"type": 0, "value": "Sind <PERSON> sicher, "}, {"type": 1, "value": "target"}, {"type": 0, "value": " zu entfernen?"}], "message.confirm-reset": [{"type": 0, "value": "Sind <PERSON> sicher, dass Sie die Statistiken von "}, {"type": 1, "value": "target"}, {"type": 0, "value": " zurücksetzen wollen?"}], "message.delete-team-warning": [{"type": 0, "value": "Ein Team zu löschen, wird auch alle Team-Websites löschen."}], "message.delete-website-warning": [{"type": 0, "value": "Alle zugehörigen Daten werden ebenfalls gelöscht."}], "message.error": [{"type": 0, "value": "<PERSON>s ist ein Fehler aufgetreten."}], "message.event-log": [{"type": 1, "value": "event"}, {"type": 0, "value": " auf "}, {"type": 1, "value": "url"}], "message.go-to-settings": [{"type": 0, "value": "Zu den Einstellungen"}], "message.incorrect-username-password": [{"type": 0, "value": "Falsches Passwort oder Benutzername."}], "message.invalid-domain": [{"type": 0, "value": "Ungültige Domain"}], "message.min-password-length": [{"type": 0, "value": "Minimale Länge von "}, {"type": 1, "value": "n"}, {"type": 0, "value": " <PERSON><PERSON><PERSON>"}], "message.new-version-available": [{"type": 0, "value": "Eine neue Version von Superlytics ist verfügbar: "}, {"type": 1, "value": "version"}], "message.no-data-available": [{"type": 0, "value": "<PERSON><PERSON> vorhand<PERSON>."}], "message.no-event-data": [{"type": 0, "value": "Es sind keine Ereignisdaten verfügbar."}], "message.no-match-password": [{"type": 0, "value": "Passwörter stimmen nicht überein"}], "message.no-results-found": [{"type": 0, "value": "<PERSON><PERSON> gefunden."}], "message.no-team-websites": [{"type": 0, "value": "Diesem Team sind keine Websites zugeordnet."}], "message.no-teams": [{"type": 0, "value": "<PERSON><PERSON>er wurden keine Teams erstellt."}], "message.no-users": [{"type": 0, "value": "Hier gibt es keine Benutzer."}], "message.no-websites-configured": [{"type": 0, "value": "<PERSON>s ist keine Website vorhanden."}], "message.page-not-found": [{"type": 0, "value": "Seite nicht gefunden."}], "message.reset-website": [{"type": 0, "value": "Um diese Website zurückzusetzen, geben Sie zur Bestätigung "}, {"type": 1, "value": "confirmation"}, {"type": 0, "value": " in das <PERSON> unten ein."}], "message.reset-website-warning": [{"type": 0, "value": "Alle Daten für diese Website werden gelöscht, jed<PERSON> bleibt der Tracking Code bestehen."}], "message.saved": [{"type": 0, "value": "Erfolgreich gespeichert."}], "message.share-url": [{"type": 0, "value": "Die Statistiken Ihrer Website sind unter folgender URL öffentlich zugänglich:"}], "message.team-already-member": [{"type": 0, "value": "Sie sind bereits Mitglied des Teams."}], "message.team-not-found": [{"type": 0, "value": "Team nicht gefunden."}], "message.team-websites-info": [{"type": 0, "value": "Websites können von jedem im Team eingesehen werden."}], "message.tracking-code": [{"type": 0, "value": "Tracking Code"}], "message.transfer-team-website-to-user": [{"type": 0, "value": "Diese Website zu Ihrem Account transferieren?"}], "message.transfer-user-website-to-team": [{"type": 0, "value": "Wählen Sie ein Team aus, zu dem die Website transferiert werden soll."}], "message.transfer-website": [{"type": 0, "value": "Übertragen Sie die Eigentümerrechte zu Ihrem Account oder einem anderen Team."}], "message.triggered-event": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "message.user-deleted": [{"type": 0, "value": "Benutzer gelöscht."}], "message.viewed-page": [{"type": 0, "value": "Seite besucht"}], "message.visitor-log": [{"type": 0, "value": "Besucher aus "}, {"type": 1, "value": "country"}, {"type": 0, "value": " <PERSON><PERSON><PERSON> "}, {"type": 1, "value": "browser"}, {"type": 0, "value": " auf "}, {"type": 1, "value": "os"}, {"type": 0, "value": " "}, {"type": 1, "value": "device"}], "message.visitors-dropped-off": [{"type": 0, "value": "Besucherverlust"}]}