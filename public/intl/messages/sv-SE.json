{"label.access-code": [{"type": 0, "value": "Åtkomstkod"}], "label.actions": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.activity": [{"type": 0, "value": "Aktivitetslogg"}], "label.add": [{"type": 0, "value": "<PERSON><PERSON><PERSON> till"}], "label.add-description": [{"type": 0, "value": "Lägg till beskrivning"}], "label.add-member": [{"type": 0, "value": "Add member"}], "label.add-step": [{"type": 0, "value": "Add step"}], "label.add-website": [{"type": 0, "value": "Lägg till webbplats"}], "label.admin": [{"type": 0, "value": "Administratör"}], "label.after": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.all": [{"type": 0, "value": "<PERSON>a"}], "label.all-time": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.analytics": [{"type": 0, "value": "Webb<PERSON><PERSON><PERSON> Ana<PERSON>"}], "label.average": [{"type": 0, "value": "Genomsnitt"}], "label.back": [{"type": 0, "value": "Tillbaka"}], "label.before": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.bounce-rate": [{"type": 0, "value": "Avvisningsfrekvens"}], "label.breakdown": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.browser": [{"type": 0, "value": "Webb<PERSON>ä<PERSON><PERSON>"}], "label.browsers": [{"type": 0, "value": "Webb<PERSON>ä<PERSON><PERSON>"}], "label.cancel": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.change-password": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.cities": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.city": [{"type": 0, "value": "Stad"}], "label.clear-all": [{"type": 0, "value": "<PERSON><PERSON> alla"}], "label.compare": [{"type": 0, "value": "Compare"}], "label.confirm": [{"type": 0, "value": "Bekräfta"}], "label.confirm-password": [{"type": 0, "value": "Bekräfta lösenord"}], "label.contains": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.continue": [{"type": 0, "value": "Fortsätt"}], "label.count": [{"type": 0, "value": "Count"}], "label.countries": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.country": [{"type": 0, "value": "Land"}], "label.create": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.create-report": [{"type": 0, "value": "Skapa rapport"}], "label.create-team": [{"type": 0, "value": "Skapa team"}], "label.create-user": [{"type": 0, "value": "Skapa användare"}], "label.created": [{"type": 0, "value": "Skapad"}], "label.created-by": [{"type": 0, "value": "Created By"}], "label.current": [{"type": 0, "value": "Current"}], "label.current-password": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.custom-range": [{"type": 0, "value": "Anpassat urval"}], "label.dashboard": [{"type": 0, "value": "Översikt"}], "label.data": [{"type": 0, "value": "Data"}], "label.date": [{"type": 0, "value": "Datum"}], "label.date-range": [{"type": 0, "value": "Tidsperiod"}], "label.day": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.default-date-range": [{"type": 0, "value": "Standard datum-urval"}], "label.delete": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.delete-report": [{"type": 0, "value": "Delete report"}], "label.delete-team": [{"type": 0, "value": "Radera team"}], "label.delete-user": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.delete-website": [{"type": 0, "value": "<PERSON><PERSON><PERSON> webbp<PERSON>s"}], "label.description": [{"type": 0, "value": "Beskrivning"}], "label.desktop": [{"type": 0, "value": "Stationär"}], "label.details": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.device": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.devices": [{"type": 0, "value": "Enheter"}], "label.dismiss": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.does-not-contain": [{"type": 0, "value": "Innehåller inte"}], "label.domain": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.dropoff": [{"type": 0, "value": "Bortfall"}], "label.edit": [{"type": 0, "value": "Rediger<PERSON>"}], "label.edit-dashboard": [{"type": 0, "value": "Redigera översikt"}], "label.edit-member": [{"type": 0, "value": "Edit member"}], "label.enable-share-url": [{"type": 0, "value": "Aktivera delningslänk"}], "label.end-step": [{"type": 0, "value": "End Step"}], "label.entry": [{"type": 0, "value": "Entry URL"}], "label.event": [{"type": 0, "value": "Händelse"}], "label.event-data": [{"type": 0, "value": "Händelsedata"}], "label.events": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.exit": [{"type": 0, "value": "Exit URL"}], "label.false": [{"type": 0, "value": "Falskt"}], "label.field": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.fields": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.filter": [{"type": 0, "value": "Filter"}], "label.filter-combined": [{"type": 0, "value": "Kombinerade"}], "label.filter-raw": [{"type": 0, "value": "Rådata"}], "label.filters": [{"type": 0, "value": "Filter"}], "label.first-seen": [{"type": 0, "value": "First seen"}], "label.funnel": [{"type": 0, "value": "Funnel"}], "label.funnel-description": [{"type": 0, "value": "Förstå omvandlingen och bortfallsfrekvensen för användare."}], "label.goal": [{"type": 0, "value": "Goal"}], "label.goals": [{"type": 0, "value": "Goals"}], "label.goals-description": [{"type": 0, "value": "Track your goals for pageviews and events."}], "label.greater-than": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.greater-than-equals": [{"type": 0, "value": "<PERSON><PERSON><PERSON> än eller lika med"}], "label.host": [{"type": 0, "value": "Host"}], "label.hosts": [{"type": 0, "value": "Hosts"}], "label.insights": [{"type": 0, "value": "Insikter"}], "label.insights-description": [{"type": 0, "value": "Dyk djupare in i din data genom att använda olika segment och filter."}], "label.is": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.is-not": [{"type": 0, "value": "Är inte"}], "label.is-not-set": [{"type": 0, "value": "Är inte inställd"}], "label.is-set": [{"type": 0, "value": "Är inställd"}], "label.join": [{"type": 0, "value": "Gå med"}], "label.join-team": [{"type": 0, "value": "Gå med i team"}], "label.journey": [{"type": 0, "value": "Journey"}], "label.journey-description": [{"type": 0, "value": "Understand how users navigate through your website."}], "label.language": [{"type": 0, "value": "Språk"}], "label.languages": [{"type": 0, "value": "Språk"}], "label.laptop": [{"type": 0, "value": "Bärbar"}], "label.last-days": [{"type": 0, "value": "Senaste "}, {"type": 1, "value": "x"}, {"type": 0, "value": " <PERSON><PERSON>na"}], "label.last-hours": [{"type": 0, "value": "Senaste "}, {"type": 1, "value": "x"}, {"type": 0, "value": " timmarna"}], "label.last-months": [{"type": 0, "value": "Last "}, {"type": 1, "value": "x"}, {"type": 0, "value": " months"}], "label.last-seen": [{"type": 0, "value": "Last seen"}], "label.leave": [{"type": 0, "value": "Lämna"}], "label.leave-team": [{"type": 0, "value": "Lämna team"}], "label.less-than": [{"type": 0, "value": "<PERSON><PERSON> än"}], "label.less-than-equals": [{"type": 0, "value": "<PERSON><PERSON> än eller lika med"}], "label.login": [{"type": 0, "value": "Logga in"}], "label.logout": [{"type": 0, "value": "Logga ut"}], "label.manage": [{"type": 0, "value": "Manage"}], "label.manager": [{"type": 0, "value": "Manager"}], "label.max": [{"type": 0, "value": "Max"}], "label.member": [{"type": 0, "value": "Member"}], "label.members": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.min": [{"type": 0, "value": "Min"}], "label.mobile": [{"type": 0, "value": "Mobil"}], "label.more": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.my-account": [{"type": 0, "value": "My account"}], "label.my-websites": [{"type": 0, "value": "<PERSON> webbplatser"}], "label.name": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.new-password": [{"type": 0, "value": "Nytt lösenord"}], "label.none": [{"type": 0, "value": "Inga"}], "label.number-of-records": [{"type": 1, "value": "x"}, {"type": 0, "value": " "}, {"offset": 0, "options": {"one": {"value": [{"type": 0, "value": "record"}]}, "other": {"value": [{"type": 0, "value": "records"}]}}, "pluralType": "cardinal", "type": 6, "value": "x"}], "label.ok": [{"type": 0, "value": "OK"}], "label.os": [{"type": 0, "value": "Operativsystem"}], "label.overview": [{"type": 0, "value": "Översikt"}], "label.owner": [{"type": 0, "value": "Ägare"}], "label.page-of": [{"type": 0, "value": "<PERSON><PERSON> "}, {"type": 1, "value": "current"}, {"type": 0, "value": " av "}, {"type": 1, "value": "total"}], "label.page-views": [{"type": 0, "value": "Sidvisningar"}], "label.pageTitle": [{"type": 0, "value": "Sidtitel"}], "label.pages": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.password": [{"type": 0, "value": "L<PERSON>senord"}], "label.path": [{"type": 0, "value": "Path"}], "label.paths": [{"type": 0, "value": "Paths"}], "label.powered-by": [{"type": 0, "value": "Drivs av "}, {"type": 1, "value": "name"}], "label.previous": [{"type": 0, "value": "Previous"}], "label.previous-period": [{"type": 0, "value": "Previous period"}], "label.previous-year": [{"type": 0, "value": "Previous year"}], "label.profile": [{"type": 0, "value": "Profil"}], "label.properties": [{"type": 0, "value": "Properties"}], "label.property": [{"type": 0, "value": "Property"}], "label.queries": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.query": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.query-parameters": [{"type": 0, "value": "Frågeparametrar"}], "label.realtime": [{"type": 0, "value": "Realtid"}], "label.referrer": [{"type": 0, "value": "Hänvisare"}], "label.referrers": [{"type": 0, "value": "Hänvisare"}], "label.refresh": [{"type": 0, "value": "Uppdatera"}], "label.regenerate": [{"type": 0, "value": "Förnya"}], "label.region": [{"type": 0, "value": "Region"}], "label.regions": [{"type": 0, "value": "<PERSON>er"}], "label.remove": [{"type": 0, "value": "<PERSON> bort"}], "label.remove-member": [{"type": 0, "value": "Remove member"}], "label.reports": [{"type": 0, "value": "Rapporter"}], "label.required": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.reset": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.reset-website": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON> webbplats"}], "label.retention": [{"type": 0, "value": "Retention"}], "label.retention-description": [{"type": 0, "value": "Mät din webbplats engagemang genom att följa hur ofta användare återvänder."}], "label.revenue": [{"type": 0, "value": "Revenue"}], "label.revenue-description": [{"type": 0, "value": "Look into your revenue across time."}], "label.revenue-property": [{"type": 0, "value": "Revenue Property"}], "label.role": [{"type": 0, "value": "Roll"}], "label.run-query": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.save": [{"type": 0, "value": "Spara"}], "label.screens": [{"type": 0, "value": "Upplösning"}], "label.search": [{"type": 0, "value": "<PERSON>ö<PERSON>"}], "label.select": [{"type": 0, "value": "Select"}], "label.select-date": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> datum"}], "label.select-role": [{"type": 0, "value": "Select role"}], "label.select-website": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.session": [{"type": 0, "value": "Session"}], "label.sessions": [{"type": 0, "value": "Sessioner"}], "label.settings": [{"type": 0, "value": "Inställningar"}], "label.share-url": [{"type": 0, "value": "Delningslänk"}], "label.single-day": [{"type": 0, "value": "En dag"}], "label.start-step": [{"type": 0, "value": "Start Step"}], "label.steps": [{"type": 0, "value": "Steps"}], "label.sum": [{"type": 0, "value": "Summa"}], "label.tablet": [{"type": 0, "value": "Surfplatta"}], "label.team": [{"type": 0, "value": "Team"}], "label.team-id": [{"type": 0, "value": "Team ID"}], "label.team-manager": [{"type": 0, "value": "Team manager"}], "label.team-member": [{"type": 0, "value": "Team-medlem"}], "label.team-name": [{"type": 0, "value": "Team namn"}], "label.team-owner": [{"type": 0, "value": "Team-<PERSON><PERSON><PERSON>"}], "label.team-view-only": [{"type": 0, "value": "Team view only"}], "label.team-websites": [{"type": 0, "value": "Team webbplatser"}], "label.teams": [{"type": 0, "value": "Team"}], "label.theme": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.this-month": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.this-week": [{"type": 0, "value": "<PERSON><PERSON> vecka"}], "label.this-year": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.timezone": [{"type": 0, "value": "T<PERSON><PERSON>"}], "label.title": [{"type": 0, "value": "Titel"}], "label.today": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.toggle-charts": [{"type": 0, "value": "Visa/göm grafer"}], "label.total": [{"type": 0, "value": "Totalt"}], "label.total-records": [{"type": 0, "value": "Totala poster"}], "label.tracking-code": [{"type": 0, "value": "Spårningskod"}], "label.transactions": [{"type": 0, "value": "Transactions"}], "label.transfer": [{"type": 0, "value": "Transfer"}], "label.transfer-website": [{"type": 0, "value": "Transfer website"}], "label.true": [{"type": 0, "value": "<PERSON>"}], "label.type": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.unique": [{"type": 0, "value": "Unikt"}], "label.unique-visitors": [{"type": 0, "value": "<PERSON><PERSON>"}], "label.uniqueCustomers": [{"type": 0, "value": "Unique Customers"}], "label.unknown": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.untitled": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.update": [{"type": 0, "value": "Update"}], "label.url": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.urls": [{"type": 0, "value": "Länkar"}], "label.user": [{"type": 0, "value": "Användare"}], "label.user-property": [{"type": 0, "value": "User Property"}], "label.username": [{"type": 0, "value": "Användarnamn"}], "label.users": [{"type": 0, "value": "Användare"}], "label.utm": [{"type": 0, "value": "UTM"}], "label.utm-description": [{"type": 0, "value": "Track your campaigns through UTM parameters."}], "label.value": [{"type": 0, "value": "<PERSON><PERSON><PERSON>"}], "label.view": [{"type": 0, "value": "Visa"}], "label.view-details": [{"type": 0, "value": "Visa detaljer"}], "label.view-only": [{"type": 0, "value": "Endast visning"}], "label.views": [{"type": 0, "value": "Visningar"}], "label.views-per-visit": [{"type": 0, "value": "Views per visit"}], "label.visit-duration": [{"type": 0, "value": "Genomsnittlig besökstid"}], "label.visitors": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "label.visits": [{"type": 0, "value": "Visits"}], "label.website": [{"type": 0, "value": "Webbp<PERSON>s"}], "label.website-id": [{"type": 0, "value": "Webbplats ID"}], "label.websites": [{"type": 0, "value": "Webbplatser"}], "label.window": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON>"}], "label.yesterday": [{"type": 0, "value": "Igår"}], "message.action-confirmation": [{"type": 0, "value": "Type "}, {"type": 1, "value": "confirmation"}, {"type": 0, "value": " in the box below to confirm."}], "message.active-users": [{"type": 1, "value": "x"}, {"type": 0, "value": " "}, {"offset": 0, "options": {"one": {"value": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, "other": {"value": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}}, "pluralType": "cardinal", "type": 6, "value": "x"}, {"type": 0, "value": " just nu"}], "message.collected-data": [{"type": 0, "value": "Collected data"}], "message.confirm-delete": [{"type": 0, "value": "Är du säker på att du vill radera "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.confirm-leave": [{"type": 0, "value": "Är du säker på att du vill lämna "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.confirm-remove": [{"type": 0, "value": "Are you sure you want to remove "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.confirm-reset": [{"type": 0, "value": "Är du säker på att du vill återställa statistiken för "}, {"type": 1, "value": "target"}, {"type": 0, "value": "?"}], "message.delete-team-warning": [{"type": 0, "value": "Deleting a team will also delete all team websites."}], "message.delete-website-warning": [{"type": 0, "value": "All tillhörande data kommer också att raderas."}], "message.error": [{"type": 0, "value": "<PERSON><PERSON><PERSON> gick fel."}], "message.event-log": [{"type": 1, "value": "event"}, {"type": 0, "value": " på "}, {"type": 1, "value": "url"}], "message.go-to-settings": [{"type": 0, "value": "Gå till inställningar"}], "message.incorrect-username-password": [{"type": 0, "value": "Felaktigt användarnamn/lösenord."}], "message.invalid-domain": [{"type": 0, "value": "Ogilt<PERSON> do<PERSON>n"}], "message.min-password-length": [{"type": 0, "value": "Minst "}, {"type": 1, "value": "n"}, {"type": 0, "value": " tecken"}], "message.new-version-available": [{"type": 0, "value": "En ny version av Superlytics "}, {"type": 1, "value": "version"}, {"type": 0, "value": " är tillg<PERSON>nglig!"}], "message.no-data-available": [{"type": 0, "value": "Ingen data tillgänglig."}], "message.no-event-data": [{"type": 0, "value": "Ingen händelsedata är tillgänglig."}], "message.no-match-password": [{"type": 0, "value": "Lösenorden matchar inte"}], "message.no-results-found": [{"type": 0, "value": "Inga resultat hittades."}], "message.no-team-websites": [{"type": 0, "value": "<PERSON> här teamet har inga webbplatser."}], "message.no-teams": [{"type": 0, "value": "Du har inte skapat några team."}], "message.no-users": [{"type": 0, "value": "Det finns inga användare."}], "message.no-websites-configured": [{"type": 0, "value": "Du har inte konfigurerat några webbplatser."}], "message.page-not-found": [{"type": 0, "value": "<PERSON><PERSON> kunde inte hittas."}], "message.reset-website": [{"type": 0, "value": "<PERSON><PERSON><PERSON> att å<PERSON><PERSON>ä<PERSON> web<PERSON>p<PERSON>, skriv "}, {"type": 1, "value": "confirmation"}, {"type": 0, "value": " i rutan nedan."}], "message.reset-website-warning": [{"type": 0, "value": "All statistik för webb<PERSON><PERSON> tas bort, men spårningskoden förblir oförändrad."}], "message.saved": [{"type": 0, "value": "Sparat!"}], "message.share-url": [{"type": 0, "value": "Det här är den offentliga delningslänken för "}, {"type": 1, "value": "target"}, {"type": 0, "value": "."}], "message.team-already-member": [{"type": 0, "value": "<PERSON> <PERSON>r redan medlem i teamet."}], "message.team-not-found": [{"type": 0, "value": "Teamet kunde inte hittas."}], "message.team-websites-info": [{"type": 0, "value": "Webbplatserna kan ses av alla i teamet."}], "message.tracking-code": [{"type": 0, "value": "Spårningskod"}], "message.transfer-team-website-to-user": [{"type": 0, "value": "Transfer this website to your account?"}], "message.transfer-user-website-to-team": [{"type": 0, "value": "Select the team to transfer this website to."}], "message.transfer-website": [{"type": 0, "value": "Transfer website ownership to your account or another team."}], "message.triggered-event": [{"type": 0, "value": "Triggered event"}], "message.user-deleted": [{"type": 0, "value": "Användaren har raderats."}], "message.viewed-page": [{"type": 0, "value": "Viewed page"}], "message.visitor-log": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON> frå<PERSON> "}, {"type": 1, "value": "country"}, {"type": 0, "value": " med "}, {"type": 1, "value": "browser"}, {"type": 0, "value": " på "}, {"type": 1, "value": "os"}, {"type": 0, "value": " "}, {"type": 1, "value": "device"}], "message.visitors-dropped-off": [{"type": 0, "value": "Visitors dropped off"}]}