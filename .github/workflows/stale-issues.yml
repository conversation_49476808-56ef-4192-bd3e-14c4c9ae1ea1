name: Close stale issues
on:
  schedule:
    - cron: '30 1 * * *'

jobs:
  stale:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@v8
        with:
          days-before-issue-stale: 60
          days-before-issue-close: 7
          stale-issue-label: 'stale'
          stale-issue-message: 'This issue is stale because it has been open for 60 days with no activity.'
          close-issue-message: 'This issue was closed because it has been inactive for 7 days since being marked as stale.'
          days-before-pr-stale: -1
          days-before-pr-close: -1
          operations-per-run: 200
          ascending: true
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          exempt-issue-labels: bug,enhancement
