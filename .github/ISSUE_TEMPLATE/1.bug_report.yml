name: '🐛 Bug Report'
description: Create a bug report for Umami.
body:
  - type: textarea
    attributes:
      label: Describe the Bug
      description: A clear and concise description of what the bug is.
    validations:
      required: true
  - type: dropdown
    attributes:
      label: Database
      description: What database are you using?
      options:
        - PostgreSQL
        - MySQL
        - Umami Cloud
    validations:
      required: true
  - type: textarea
    attributes:
      label: Relevant log output
      description: Please copy and paste any relevant log output. This will be automatically formatted into code, so no need for backticks.
      render: shell
  - type: input
    attributes:
      label: Which Umami version are you using? (if relevant)
      description: 'For example: 2.18.0, 2.15.1, 1.39.0, etc'
  - type: input
    attributes:
      label: Which browser are you using? (if relevant)
      description: 'For example: Chrome, Edge, Firefox, etc'
  - type: input
    attributes:
      label: How are you deploying your application? (if relevant)
      description: 'For example: Vercel, Railway, Docker, etc'
