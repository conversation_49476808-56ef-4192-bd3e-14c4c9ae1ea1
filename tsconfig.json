{"compilerOptions": {"target": "es2022", "outDir": "./build", "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "lib": ["dom", "dom.iterable", "esnext"], "skipLibCheck": true, "esModuleInterop": true, "noImplicitAny": false, "preserveConstEnums": true, "removeComments": true, "sourceMap": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "allowJs": true, "strict": true, "strictNullChecks": false, "noEmit": true, "jsx": "preserve", "incremental": false, "types": ["jest"], "typeRoots": ["node_modules/@types"], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "plugins": [{"name": "next"}]}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "next-env.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules", "./cypress.config.ts", "cypress"]}